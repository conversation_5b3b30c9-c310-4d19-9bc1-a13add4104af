# Create a new React project with TypeScript
npx create-react-app igcse-student-guide --template typescript

# Navigate to project directory
cd igcse-student-guide

# Install Tailwind CSS and dependencies
npm install -D tailwindcss postcss autoprefixer
npx tailwindcss init -p

# Install other required dependencies
npm install react-router-dom @supabase/supabase-js @supabase/auth-helpers-react @supabase/auth-ui-react