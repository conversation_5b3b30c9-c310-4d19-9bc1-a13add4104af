import React from 'react';

const QuizzesPage: React.FC = () => {
  return (
    <div>
      <div className="mb-8">
        <h1>Practice Quizzes</h1>
        <p className="text-neutral-600">
          Test your knowledge with quizzes designed to reinforce learning and identify gaps.
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-lg font-semibold mb-2">Quizzes Taken</h3>
          <p className="text-3xl font-bold text-primary-600">0</p>
          <p className="text-neutral-500 text-sm mt-1">This week</p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-lg font-semibold mb-2">Average Score</h3>
          <p className="text-3xl font-bold text-primary-600">--%</p>
          <p className="text-neutral-500 text-sm mt-1">Across all quizzes</p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-lg font-semibold mb-2">Best Subject</h3>
          <p className="text-3xl font-bold text-primary-600">--</p>
          <p className="text-neutral-500 text-sm mt-1">Highest average</p>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {/* Placeholder quiz cards */}
        {[
          { subject: 'Mathematics', topic: 'Algebra Basics', questions: 15, difficulty: 'Easy' },
          { subject: 'Physics', topic: 'Forces and Motion', questions: 12, difficulty: 'Medium' },
          { subject: 'Chemistry', topic: 'Atomic Structure', questions: 10, difficulty: 'Medium' },
          { subject: 'Biology', topic: 'Cell Structure', questions: 18, difficulty: 'Easy' },
          { subject: 'English', topic: 'Grammar Fundamentals', questions: 20, difficulty: 'Easy' },
          { subject: 'History', topic: 'World War I', questions: 14, difficulty: 'Hard' },
        ].map((quiz, index) => (
          <div key={index} className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow">
            <div className="flex justify-between items-start mb-3">
              <h3 className="text-lg font-semibold">{quiz.topic}</h3>
              <span className={`px-2 py-1 text-xs rounded-full ${
                quiz.difficulty === 'Easy' ? 'bg-green-100 text-green-800' :
                quiz.difficulty === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              }`}>
                {quiz.difficulty}
              </span>
            </div>
            <p className="text-neutral-600 mb-2">{quiz.subject}</p>
            <p className="text-sm text-neutral-500 mb-4">{quiz.questions} questions</p>
            <button className="w-full bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out">
              Start Quiz
            </button>
          </div>
        ))}
      </div>
      
      <div className="bg-secondary-50 p-6 rounded-lg">
        <h2 className="text-xl font-semibold mb-2">Quiz Features Coming Soon</h2>
        <ul className="space-y-2 text-neutral-600">
          <li className="flex items-center">
            <svg className="w-5 h-5 text-secondary-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            Interactive multiple-choice questions
          </li>
          <li className="flex items-center">
            <svg className="w-5 h-5 text-secondary-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            Immediate feedback and explanations
          </li>
          <li className="flex items-center">
            <svg className="w-5 h-5 text-secondary-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            Detailed performance analytics
          </li>
          <li className="flex items-center">
            <svg className="w-5 h-5 text-secondary-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            Adaptive difficulty levels
          </li>
        </ul>
      </div>
    </div>
  );
};

export default QuizzesPage;
