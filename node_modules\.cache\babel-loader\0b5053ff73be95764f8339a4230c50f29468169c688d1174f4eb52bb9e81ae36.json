{"ast": null, "code": "var _jsxFileName = \"D:\\\\GrowthSch\\\\IGCSEStuGuide\\\\src\\\\components\\\\layout\\\\AppShell.tsx\";\nimport React from 'react';\nimport Navbar from './Navbar';\nimport Footer from './Footer';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AppShell = ({\n  children\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col min-h-screen\",\n    children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"flex-grow container mx-auto px-4 py-8\",\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n};\n_c = AppShell;\nexport default AppShell;\nvar _c;\n$RefreshReg$(_c, \"AppShell\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "Footer", "jsxDEV", "_jsxDEV", "AppShell", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/GrowthSch/IGCSEStuGuide/src/components/layout/AppShell.tsx"], "sourcesContent": ["import React from 'react';\nimport Navbar from './Navbar';\nimport Footer from './Footer';\n\ninterface AppShellProps {\n  children: React.ReactNode;\n}\n\nconst AppShell: React.FC<AppShellProps> = ({ children }) => {\n  return (\n    <div className=\"flex flex-col min-h-screen\">\n      <Navbar />\n      <main className=\"flex-grow container mx-auto px-4 py-8\">\n        {children}\n      </main>\n      <Footer />\n    </div>\n  );\n};\n\nexport default AppShell;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,MAAM,MAAM,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM9B,MAAMC,QAAiC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAC1D,oBACEF,OAAA;IAAKG,SAAS,EAAC,4BAA4B;IAAAD,QAAA,gBACzCF,OAAA,CAACH,MAAM;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVP,OAAA;MAAMG,SAAS,EAAC,uCAAuC;MAAAD,QAAA,EACpDA;IAAQ;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACPP,OAAA,CAACF,MAAM;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACC,EAAA,GAVIP,QAAiC;AAYvC,eAAeA,QAAQ;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}