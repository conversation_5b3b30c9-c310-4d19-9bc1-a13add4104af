{"ast": null, "code": "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nimport { FunctionsClient } from '@supabase/functions-js';\nimport { PostgrestClient } from '@supabase/postgrest-js';\nimport { RealtimeClient } from '@supabase/realtime-js';\nimport { StorageClient as SupabaseStorageClient } from '@supabase/storage-js';\nimport { DEFAULT_GLOBAL_OPTIONS, DEFAULT_DB_OPTIONS, DEFAULT_AUTH_OPTIONS, DEFAULT_REALTIME_OPTIONS } from './lib/constants';\nimport { fetchWithAuth } from './lib/fetch';\nimport { ensureTrailingSlash, applySettingDefaults } from './lib/helpers';\nimport { SupabaseAuthClient } from './lib/SupabaseAuthClient';\n/**\n * Supabase Client.\n *\n * An isomorphic Javascript client for interacting with Postgres.\n */\nexport default class SupabaseClient {\n  /**\n   * Create a new client for use in the browser.\n   * @param supabaseUrl The unique Supabase URL which is supplied when you create a new project in your project dashboard.\n   * @param supabaseKey The unique Supabase Key which is supplied when you create a new project in your project dashboard.\n   * @param options.db.schema You can switch in between schemas. The schema needs to be on the list of exposed schemas inside Supabase.\n   * @param options.auth.autoRefreshToken Set to \"true\" if you want to automatically refresh the token before expiring.\n   * @param options.auth.persistSession Set to \"true\" if you want to automatically save the user session into local storage.\n   * @param options.auth.detectSessionInUrl Set to \"true\" if you want to automatically detects OAuth grants in the URL and signs in the user.\n   * @param options.realtime Options passed along to realtime-js constructor.\n   * @param options.global.fetch A custom fetch implementation.\n   * @param options.global.headers Any additional headers to send with each network request.\n   */\n  constructor(supabaseUrl, supabaseKey, options) {\n    var _a, _b, _c;\n    this.supabaseUrl = supabaseUrl;\n    this.supabaseKey = supabaseKey;\n    if (!supabaseUrl) throw new Error('supabaseUrl is required.');\n    if (!supabaseKey) throw new Error('supabaseKey is required.');\n    const _supabaseUrl = ensureTrailingSlash(supabaseUrl);\n    const baseUrl = new URL(_supabaseUrl);\n    this.realtimeUrl = new URL('realtime/v1', baseUrl);\n    this.realtimeUrl.protocol = this.realtimeUrl.protocol.replace('http', 'ws');\n    this.authUrl = new URL('auth/v1', baseUrl);\n    this.storageUrl = new URL('storage/v1', baseUrl);\n    this.functionsUrl = new URL('functions/v1', baseUrl);\n    // default storage key uses the supabase project ref as a namespace\n    const defaultStorageKey = `sb-${baseUrl.hostname.split('.')[0]}-auth-token`;\n    const DEFAULTS = {\n      db: DEFAULT_DB_OPTIONS,\n      realtime: DEFAULT_REALTIME_OPTIONS,\n      auth: Object.assign(Object.assign({}, DEFAULT_AUTH_OPTIONS), {\n        storageKey: defaultStorageKey\n      }),\n      global: DEFAULT_GLOBAL_OPTIONS\n    };\n    const settings = applySettingDefaults(options !== null && options !== void 0 ? options : {}, DEFAULTS);\n    this.storageKey = (_a = settings.auth.storageKey) !== null && _a !== void 0 ? _a : '';\n    this.headers = (_b = settings.global.headers) !== null && _b !== void 0 ? _b : {};\n    if (!settings.accessToken) {\n      this.auth = this._initSupabaseAuthClient((_c = settings.auth) !== null && _c !== void 0 ? _c : {}, this.headers, settings.global.fetch);\n    } else {\n      this.accessToken = settings.accessToken;\n      this.auth = new Proxy({}, {\n        get: (_, prop) => {\n          throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(prop)} is not possible`);\n        }\n      });\n    }\n    this.fetch = fetchWithAuth(supabaseKey, this._getAccessToken.bind(this), settings.global.fetch);\n    this.realtime = this._initRealtimeClient(Object.assign({\n      headers: this.headers,\n      accessToken: this._getAccessToken.bind(this)\n    }, settings.realtime));\n    this.rest = new PostgrestClient(new URL('rest/v1', baseUrl).href, {\n      headers: this.headers,\n      schema: settings.db.schema,\n      fetch: this.fetch\n    });\n    if (!settings.accessToken) {\n      this._listenForAuthEvents();\n    }\n  }\n  /**\n   * Supabase Functions allows you to deploy and invoke edge functions.\n   */\n  get functions() {\n    return new FunctionsClient(this.functionsUrl.href, {\n      headers: this.headers,\n      customFetch: this.fetch\n    });\n  }\n  /**\n   * Supabase Storage allows you to manage user-generated content, such as photos or videos.\n   */\n  get storage() {\n    return new SupabaseStorageClient(this.storageUrl.href, this.headers, this.fetch);\n  }\n  /**\n   * Perform a query on a table or a view.\n   *\n   * @param relation - The table or view name to query\n   */\n  from(relation) {\n    return this.rest.from(relation);\n  }\n  // NOTE: signatures must be kept in sync with PostgrestClient.schema\n  /**\n   * Select a schema to query or perform an function (rpc) call.\n   *\n   * The schema needs to be on the list of exposed schemas inside Supabase.\n   *\n   * @param schema - The schema to query\n   */\n  schema(schema) {\n    return this.rest.schema(schema);\n  }\n  // NOTE: signatures must be kept in sync with PostgrestClient.rpc\n  /**\n   * Perform a function call.\n   *\n   * @param fn - The function name to call\n   * @param args - The arguments to pass to the function call\n   * @param options - Named parameters\n   * @param options.head - When set to `true`, `data` will not be returned.\n   * Useful if you only need the count.\n   * @param options.get - When set to `true`, the function will be called with\n   * read-only access mode.\n   * @param options.count - Count algorithm to use to count rows returned by the\n   * function. Only applicable for [set-returning\n   * functions](https://www.postgresql.org/docs/current/functions-srf.html).\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   */\n  rpc(fn, args = {}, options = {}) {\n    return this.rest.rpc(fn, args, options);\n  }\n  /**\n   * Creates a Realtime channel with Broadcast, Presence, and Postgres Changes.\n   *\n   * @param {string} name - The name of the Realtime channel.\n   * @param {Object} opts - The options to pass to the Realtime channel.\n   *\n   */\n  channel(name, opts = {\n    config: {}\n  }) {\n    return this.realtime.channel(name, opts);\n  }\n  /**\n   * Returns all Realtime channels.\n   */\n  getChannels() {\n    return this.realtime.getChannels();\n  }\n  /**\n   * Unsubscribes and removes Realtime channel from Realtime client.\n   *\n   * @param {RealtimeChannel} channel - The name of the Realtime channel.\n   *\n   */\n  removeChannel(channel) {\n    return this.realtime.removeChannel(channel);\n  }\n  /**\n   * Unsubscribes and removes all Realtime channels from Realtime client.\n   */\n  removeAllChannels() {\n    return this.realtime.removeAllChannels();\n  }\n  _getAccessToken() {\n    var _a, _b;\n    return __awaiter(this, void 0, void 0, function* () {\n      if (this.accessToken) {\n        return yield this.accessToken();\n      }\n      const {\n        data\n      } = yield this.auth.getSession();\n      return (_b = (_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token) !== null && _b !== void 0 ? _b : null;\n    });\n  }\n  _initSupabaseAuthClient({\n    autoRefreshToken,\n    persistSession,\n    detectSessionInUrl,\n    storage,\n    storageKey,\n    flowType,\n    lock,\n    debug\n  }, headers, fetch) {\n    const authHeaders = {\n      Authorization: `Bearer ${this.supabaseKey}`,\n      apikey: `${this.supabaseKey}`\n    };\n    return new SupabaseAuthClient({\n      url: this.authUrl.href,\n      headers: Object.assign(Object.assign({}, authHeaders), headers),\n      storageKey: storageKey,\n      autoRefreshToken,\n      persistSession,\n      detectSessionInUrl,\n      storage,\n      flowType,\n      lock,\n      debug,\n      fetch,\n      // auth checks if there is a custom authorizaiton header using this flag\n      // so it knows whether to return an error when getUser is called with no session\n      hasCustomAuthorizationHeader: 'Authorization' in this.headers\n    });\n  }\n  _initRealtimeClient(options) {\n    return new RealtimeClient(this.realtimeUrl.href, Object.assign(Object.assign({}, options), {\n      params: Object.assign({\n        apikey: this.supabaseKey\n      }, options === null || options === void 0 ? void 0 : options.params)\n    }));\n  }\n  _listenForAuthEvents() {\n    let data = this.auth.onAuthStateChange((event, session) => {\n      this._handleTokenChanged(event, 'CLIENT', session === null || session === void 0 ? void 0 : session.access_token);\n    });\n    return data;\n  }\n  _handleTokenChanged(event, source, token) {\n    if ((event === 'TOKEN_REFRESHED' || event === 'SIGNED_IN') && this.changedAccessToken !== token) {\n      this.changedAccessToken = token;\n    } else if (event === 'SIGNED_OUT') {\n      this.realtime.setAuth();\n      if (source == 'STORAGE') this.auth.signOut();\n      this.changedAccessToken = undefined;\n    }\n  }\n}", "map": {"version": 3, "names": ["FunctionsClient", "PostgrestClient", "RealtimeClient", "StorageClient", "SupabaseStorageClient", "DEFAULT_GLOBAL_OPTIONS", "DEFAULT_DB_OPTIONS", "DEFAULT_AUTH_OPTIONS", "DEFAULT_REALTIME_OPTIONS", "fetchWithAuth", "ensureTrailingSlash", "applySettingDefaults", "SupabaseAuthClient", "SupabaseClient", "constructor", "supabaseUrl", "supabase<PERSON>ey", "options", "Error", "_supabaseUrl", "baseUrl", "URL", "realtimeUrl", "protocol", "replace", "authUrl", "storageUrl", "functionsUrl", "defaultStorageKey", "hostname", "split", "DEFAULTS", "db", "realtime", "auth", "Object", "assign", "storageKey", "global", "settings", "_a", "headers", "_b", "accessToken", "_initSupabaseAuthClient", "_c", "fetch", "Proxy", "get", "_", "prop", "String", "_getAccessToken", "bind", "_initRealtimeClient", "rest", "href", "schema", "_listenForAuthEvents", "functions", "customFetch", "storage", "from", "relation", "rpc", "fn", "args", "channel", "name", "opts", "config", "getChannels", "removeChannel", "removeAllChannels", "data", "getSession", "session", "access_token", "autoRefreshToken", "persistSession", "detectSessionInUrl", "flowType", "lock", "debug", "authHeaders", "Authorization", "apikey", "url", "hasCustomAuthorizationHeader", "params", "onAuthStateChange", "event", "_handleTokenChanged", "source", "token", "changedAccessToken", "setAuth", "signOut", "undefined"], "sources": ["D:\\GrowthSch\\IGCSEStuGuide\\node_modules\\@supabase\\supabase-js\\src\\SupabaseClient.ts"], "sourcesContent": ["import { FunctionsClient } from '@supabase/functions-js'\nimport { AuthChangeEvent } from '@supabase/auth-js'\nimport {\n  PostgrestClient,\n  PostgrestFilterBuilder,\n  PostgrestQueryBuilder,\n} from '@supabase/postgrest-js'\nimport {\n  RealtimeChannel,\n  RealtimeChannelOptions,\n  RealtimeClient,\n  RealtimeClientOptions,\n} from '@supabase/realtime-js'\nimport { StorageClient as SupabaseStorageClient } from '@supabase/storage-js'\nimport {\n  DEFAULT_GLOBAL_OPTIONS,\n  DEFAULT_DB_OPTIONS,\n  DEFAULT_AUTH_OPTIONS,\n  DEFAULT_REALTIME_OPTIONS,\n} from './lib/constants'\nimport { fetchWithAuth } from './lib/fetch'\nimport { ensureTrailingSlash, applySettingDefaults } from './lib/helpers'\nimport { SupabaseAuthClient } from './lib/SupabaseAuthClient'\nimport { Fetch, GenericSchema, SupabaseClientOptions, SupabaseAuthClientOptions } from './lib/types'\n\n/**\n * Supabase Client.\n *\n * An isomorphic Javascript client for interacting with Postgres.\n */\nexport default class SupabaseClient<\n  Database = any,\n  SchemaName extends string & keyof Database = 'public' extends keyof Database\n    ? 'public'\n    : string & keyof Database,\n  Schema extends GenericSchema = Database[SchemaName] extends GenericSchema\n    ? Database[SchemaName]\n    : any\n> {\n  /**\n   * Supabase Auth allows you to create and manage user sessions for access to data that is secured by access policies.\n   */\n  auth: SupabaseAuthClient\n  realtime: RealtimeClient\n\n  protected realtimeUrl: URL\n  protected authUrl: URL\n  protected storageUrl: URL\n  protected functionsUrl: URL\n  protected rest: PostgrestClient<Database, SchemaName, Schema>\n  protected storageKey: string\n  protected fetch?: Fetch\n  protected changedAccessToken?: string\n  protected accessToken?: () => Promise<string | null>\n\n  protected headers: Record<string, string>\n\n  /**\n   * Create a new client for use in the browser.\n   * @param supabaseUrl The unique Supabase URL which is supplied when you create a new project in your project dashboard.\n   * @param supabaseKey The unique Supabase Key which is supplied when you create a new project in your project dashboard.\n   * @param options.db.schema You can switch in between schemas. The schema needs to be on the list of exposed schemas inside Supabase.\n   * @param options.auth.autoRefreshToken Set to \"true\" if you want to automatically refresh the token before expiring.\n   * @param options.auth.persistSession Set to \"true\" if you want to automatically save the user session into local storage.\n   * @param options.auth.detectSessionInUrl Set to \"true\" if you want to automatically detects OAuth grants in the URL and signs in the user.\n   * @param options.realtime Options passed along to realtime-js constructor.\n   * @param options.global.fetch A custom fetch implementation.\n   * @param options.global.headers Any additional headers to send with each network request.\n   */\n  constructor(\n    protected supabaseUrl: string,\n    protected supabaseKey: string,\n    options?: SupabaseClientOptions<SchemaName>\n  ) {\n    if (!supabaseUrl) throw new Error('supabaseUrl is required.')\n    if (!supabaseKey) throw new Error('supabaseKey is required.')\n\n    const _supabaseUrl = ensureTrailingSlash(supabaseUrl)\n    const baseUrl = new URL(_supabaseUrl)\n\n    this.realtimeUrl = new URL('realtime/v1', baseUrl)\n    this.realtimeUrl.protocol = this.realtimeUrl.protocol.replace('http', 'ws')\n    this.authUrl = new URL('auth/v1', baseUrl)\n    this.storageUrl = new URL('storage/v1', baseUrl)\n    this.functionsUrl = new URL('functions/v1', baseUrl)\n\n    // default storage key uses the supabase project ref as a namespace\n    const defaultStorageKey = `sb-${baseUrl.hostname.split('.')[0]}-auth-token`\n    const DEFAULTS = {\n      db: DEFAULT_DB_OPTIONS,\n      realtime: DEFAULT_REALTIME_OPTIONS,\n      auth: { ...DEFAULT_AUTH_OPTIONS, storageKey: defaultStorageKey },\n      global: DEFAULT_GLOBAL_OPTIONS,\n    }\n\n    const settings = applySettingDefaults(options ?? {}, DEFAULTS)\n\n    this.storageKey = settings.auth.storageKey ?? ''\n    this.headers = settings.global.headers ?? {}\n\n    if (!settings.accessToken) {\n      this.auth = this._initSupabaseAuthClient(\n        settings.auth ?? {},\n        this.headers,\n        settings.global.fetch\n      )\n    } else {\n      this.accessToken = settings.accessToken\n\n      this.auth = new Proxy<SupabaseAuthClient>({} as any, {\n        get: (_, prop) => {\n          throw new Error(\n            `@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(\n              prop\n            )} is not possible`\n          )\n        },\n      })\n    }\n\n    this.fetch = fetchWithAuth(supabaseKey, this._getAccessToken.bind(this), settings.global.fetch)\n    this.realtime = this._initRealtimeClient({\n      headers: this.headers,\n      accessToken: this._getAccessToken.bind(this),\n      ...settings.realtime,\n    })\n    this.rest = new PostgrestClient(new URL('rest/v1', baseUrl).href, {\n      headers: this.headers,\n      schema: settings.db.schema,\n      fetch: this.fetch,\n    })\n\n    if (!settings.accessToken) {\n      this._listenForAuthEvents()\n    }\n  }\n\n  /**\n   * Supabase Functions allows you to deploy and invoke edge functions.\n   */\n  get functions(): FunctionsClient {\n    return new FunctionsClient(this.functionsUrl.href, {\n      headers: this.headers,\n      customFetch: this.fetch,\n    })\n  }\n\n  /**\n   * Supabase Storage allows you to manage user-generated content, such as photos or videos.\n   */\n  get storage(): SupabaseStorageClient {\n    return new SupabaseStorageClient(this.storageUrl.href, this.headers, this.fetch)\n  }\n\n  // NOTE: signatures must be kept in sync with PostgrestClient.from\n  from<\n    TableName extends string & keyof Schema['Tables'],\n    Table extends Schema['Tables'][TableName]\n  >(relation: TableName): PostgrestQueryBuilder<Schema, Table, TableName>\n  from<ViewName extends string & keyof Schema['Views'], View extends Schema['Views'][ViewName]>(\n    relation: ViewName\n  ): PostgrestQueryBuilder<Schema, View, ViewName>\n  /**\n   * Perform a query on a table or a view.\n   *\n   * @param relation - The table or view name to query\n   */\n  from(relation: string): PostgrestQueryBuilder<Schema, any, any> {\n    return this.rest.from(relation)\n  }\n\n  // NOTE: signatures must be kept in sync with PostgrestClient.schema\n  /**\n   * Select a schema to query or perform an function (rpc) call.\n   *\n   * The schema needs to be on the list of exposed schemas inside Supabase.\n   *\n   * @param schema - The schema to query\n   */\n  schema<DynamicSchema extends string & keyof Database>(\n    schema: DynamicSchema\n  ): PostgrestClient<\n    Database,\n    DynamicSchema,\n    Database[DynamicSchema] extends GenericSchema ? Database[DynamicSchema] : any\n  > {\n    return this.rest.schema<DynamicSchema>(schema)\n  }\n\n  // NOTE: signatures must be kept in sync with PostgrestClient.rpc\n  /**\n   * Perform a function call.\n   *\n   * @param fn - The function name to call\n   * @param args - The arguments to pass to the function call\n   * @param options - Named parameters\n   * @param options.head - When set to `true`, `data` will not be returned.\n   * Useful if you only need the count.\n   * @param options.get - When set to `true`, the function will be called with\n   * read-only access mode.\n   * @param options.count - Count algorithm to use to count rows returned by the\n   * function. Only applicable for [set-returning\n   * functions](https://www.postgresql.org/docs/current/functions-srf.html).\n   *\n   * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n   * hood.\n   *\n   * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n   * statistics under the hood.\n   *\n   * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n   * numbers.\n   */\n  rpc<FnName extends string & keyof Schema['Functions'], Fn extends Schema['Functions'][FnName]>(\n    fn: FnName,\n    args: Fn['Args'] = {},\n    options: {\n      head?: boolean\n      get?: boolean\n      count?: 'exact' | 'planned' | 'estimated'\n    } = {}\n  ): PostgrestFilterBuilder<\n    Schema,\n    Fn['Returns'] extends any[]\n      ? Fn['Returns'][number] extends Record<string, unknown>\n        ? Fn['Returns'][number]\n        : never\n      : never,\n    Fn['Returns'],\n    FnName,\n    null\n  > {\n    return this.rest.rpc(fn, args, options)\n  }\n\n  /**\n   * Creates a Realtime channel with Broadcast, Presence, and Postgres Changes.\n   *\n   * @param {string} name - The name of the Realtime channel.\n   * @param {Object} opts - The options to pass to the Realtime channel.\n   *\n   */\n  channel(name: string, opts: RealtimeChannelOptions = { config: {} }): RealtimeChannel {\n    return this.realtime.channel(name, opts)\n  }\n\n  /**\n   * Returns all Realtime channels.\n   */\n  getChannels(): RealtimeChannel[] {\n    return this.realtime.getChannels()\n  }\n\n  /**\n   * Unsubscribes and removes Realtime channel from Realtime client.\n   *\n   * @param {RealtimeChannel} channel - The name of the Realtime channel.\n   *\n   */\n  removeChannel(channel: RealtimeChannel): Promise<'ok' | 'timed out' | 'error'> {\n    return this.realtime.removeChannel(channel)\n  }\n\n  /**\n   * Unsubscribes and removes all Realtime channels from Realtime client.\n   */\n  removeAllChannels(): Promise<('ok' | 'timed out' | 'error')[]> {\n    return this.realtime.removeAllChannels()\n  }\n\n  private async _getAccessToken() {\n    if (this.accessToken) {\n      return await this.accessToken()\n    }\n\n    const { data } = await this.auth.getSession()\n\n    return data.session?.access_token ?? null\n  }\n\n  private _initSupabaseAuthClient(\n    {\n      autoRefreshToken,\n      persistSession,\n      detectSessionInUrl,\n      storage,\n      storageKey,\n      flowType,\n      lock,\n      debug,\n    }: SupabaseAuthClientOptions,\n    headers?: Record<string, string>,\n    fetch?: Fetch\n  ) {\n    const authHeaders = {\n      Authorization: `Bearer ${this.supabaseKey}`,\n      apikey: `${this.supabaseKey}`,\n    }\n    return new SupabaseAuthClient({\n      url: this.authUrl.href,\n      headers: { ...authHeaders, ...headers },\n      storageKey: storageKey,\n      autoRefreshToken,\n      persistSession,\n      detectSessionInUrl,\n      storage,\n      flowType,\n      lock,\n      debug,\n      fetch,\n      // auth checks if there is a custom authorizaiton header using this flag\n      // so it knows whether to return an error when getUser is called with no session\n      hasCustomAuthorizationHeader: 'Authorization' in this.headers,\n    })\n  }\n\n  private _initRealtimeClient(options: RealtimeClientOptions) {\n    return new RealtimeClient(this.realtimeUrl.href, {\n      ...options,\n      params: { ...{ apikey: this.supabaseKey }, ...options?.params },\n    })\n  }\n\n  private _listenForAuthEvents() {\n    let data = this.auth.onAuthStateChange((event, session) => {\n      this._handleTokenChanged(event, 'CLIENT', session?.access_token)\n    })\n    return data\n  }\n\n  private _handleTokenChanged(\n    event: AuthChangeEvent,\n    source: 'CLIENT' | 'STORAGE',\n    token?: string\n  ) {\n    if (\n      (event === 'TOKEN_REFRESHED' || event === 'SIGNED_IN') &&\n      this.changedAccessToken !== token\n    ) {\n      this.changedAccessToken = token\n    } else if (event === 'SIGNED_OUT') {\n      this.realtime.setAuth()\n      if (source == 'STORAGE') this.auth.signOut()\n      this.changedAccessToken = undefined\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAASA,eAAe,QAAQ,wBAAwB;AAExD,SACEC,eAAe,QAGV,wBAAwB;AAC/B,SAGEC,cAAc,QAET,uBAAuB;AAC9B,SAASC,aAAa,IAAIC,qBAAqB,QAAQ,sBAAsB;AAC7E,SACEC,sBAAsB,EACtBC,kBAAkB,EAClBC,oBAAoB,EACpBC,wBAAwB,QACnB,iBAAiB;AACxB,SAASC,aAAa,QAAQ,aAAa;AAC3C,SAASC,mBAAmB,EAAEC,oBAAoB,QAAQ,eAAe;AACzE,SAASC,kBAAkB,QAAQ,0BAA0B;AAG7D;;;;;AAKA,eAAc,MAAOC,cAAc;EA2BjC;;;;;;;;;;;;EAYAC,YACYC,WAAmB,EACnBC,WAAmB,EAC7BC,OAA2C;;IAFjC,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IAGrB,IAAI,CAACD,WAAW,EAAE,MAAM,IAAIG,KAAK,CAAC,0BAA0B,CAAC;IAC7D,IAAI,CAACF,WAAW,EAAE,MAAM,IAAIE,KAAK,CAAC,0BAA0B,CAAC;IAE7D,MAAMC,YAAY,GAAGT,mBAAmB,CAACK,WAAW,CAAC;IACrD,MAAMK,OAAO,GAAG,IAAIC,GAAG,CAACF,YAAY,CAAC;IAErC,IAAI,CAACG,WAAW,GAAG,IAAID,GAAG,CAAC,aAAa,EAAED,OAAO,CAAC;IAClD,IAAI,CAACE,WAAW,CAACC,QAAQ,GAAG,IAAI,CAACD,WAAW,CAACC,QAAQ,CAACC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC;IAC3E,IAAI,CAACC,OAAO,GAAG,IAAIJ,GAAG,CAAC,SAAS,EAAED,OAAO,CAAC;IAC1C,IAAI,CAACM,UAAU,GAAG,IAAIL,GAAG,CAAC,YAAY,EAAED,OAAO,CAAC;IAChD,IAAI,CAACO,YAAY,GAAG,IAAIN,GAAG,CAAC,cAAc,EAAED,OAAO,CAAC;IAEpD;IACA,MAAMQ,iBAAiB,GAAG,MAAMR,OAAO,CAACS,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa;IAC3E,MAAMC,QAAQ,GAAG;MACfC,EAAE,EAAE1B,kBAAkB;MACtB2B,QAAQ,EAAEzB,wBAAwB;MAClC0B,IAAI,EAAAC,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAO7B,oBAAoB;QAAE8B,UAAU,EAAET;MAAiB,EAAE;MAChEU,MAAM,EAAEjC;KACT;IAED,MAAMkC,QAAQ,GAAG5B,oBAAoB,CAACM,OAAO,aAAPA,OAAO,cAAPA,OAAO,GAAI,EAAE,EAAEc,QAAQ,CAAC;IAE9D,IAAI,CAACM,UAAU,GAAG,CAAAG,EAAA,GAAAD,QAAQ,CAACL,IAAI,CAACG,UAAU,cAAAG,EAAA,cAAAA,EAAA,GAAI,EAAE;IAChD,IAAI,CAACC,OAAO,GAAG,CAAAC,EAAA,GAAAH,QAAQ,CAACD,MAAM,CAACG,OAAO,cAAAC,EAAA,cAAAA,EAAA,GAAI,EAAE;IAE5C,IAAI,CAACH,QAAQ,CAACI,WAAW,EAAE;MACzB,IAAI,CAACT,IAAI,GAAG,IAAI,CAACU,uBAAuB,CACtC,CAAAC,EAAA,GAAAN,QAAQ,CAACL,IAAI,cAAAW,EAAA,cAAAA,EAAA,GAAI,EAAE,EACnB,IAAI,CAACJ,OAAO,EACZF,QAAQ,CAACD,MAAM,CAACQ,KAAK,CACtB;KACF,MAAM;MACL,IAAI,CAACH,WAAW,GAAGJ,QAAQ,CAACI,WAAW;MAEvC,IAAI,CAACT,IAAI,GAAG,IAAIa,KAAK,CAAqB,EAAS,EAAE;QACnDC,GAAG,EAAEA,CAACC,CAAC,EAAEC,IAAI,KAAI;UACf,MAAM,IAAIhC,KAAK,CACb,6GAA6GiC,MAAM,CACjHD,IAAI,CACL,kBAAkB,CACpB;QACH;OACD,CAAC;;IAGJ,IAAI,CAACJ,KAAK,GAAGrC,aAAa,CAACO,WAAW,EAAE,IAAI,CAACoC,eAAe,CAACC,IAAI,CAAC,IAAI,CAAC,EAAEd,QAAQ,CAACD,MAAM,CAACQ,KAAK,CAAC;IAC/F,IAAI,CAACb,QAAQ,GAAG,IAAI,CAACqB,mBAAmB,CAAAnB,MAAA,CAAAC,MAAA;MACtCK,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBE,WAAW,EAAE,IAAI,CAACS,eAAe,CAACC,IAAI,CAAC,IAAI;IAAC,GACzCd,QAAQ,CAACN,QAAQ,EACpB;IACF,IAAI,CAACsB,IAAI,GAAG,IAAItD,eAAe,CAAC,IAAIoB,GAAG,CAAC,SAAS,EAAED,OAAO,CAAC,CAACoC,IAAI,EAAE;MAChEf,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBgB,MAAM,EAAElB,QAAQ,CAACP,EAAE,CAACyB,MAAM;MAC1BX,KAAK,EAAE,IAAI,CAACA;KACb,CAAC;IAEF,IAAI,CAACP,QAAQ,CAACI,WAAW,EAAE;MACzB,IAAI,CAACe,oBAAoB,EAAE;;EAE/B;EAEA;;;EAGA,IAAIC,SAASA,CAAA;IACX,OAAO,IAAI3D,eAAe,CAAC,IAAI,CAAC2B,YAAY,CAAC6B,IAAI,EAAE;MACjDf,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBmB,WAAW,EAAE,IAAI,CAACd;KACnB,CAAC;EACJ;EAEA;;;EAGA,IAAIe,OAAOA,CAAA;IACT,OAAO,IAAIzD,qBAAqB,CAAC,IAAI,CAACsB,UAAU,CAAC8B,IAAI,EAAE,IAAI,CAACf,OAAO,EAAE,IAAI,CAACK,KAAK,CAAC;EAClF;EAUA;;;;;EAKAgB,IAAIA,CAACC,QAAgB;IACnB,OAAO,IAAI,CAACR,IAAI,CAACO,IAAI,CAACC,QAAQ,CAAC;EACjC;EAEA;EACA;;;;;;;EAOAN,MAAMA,CACJA,MAAqB;IAMrB,OAAO,IAAI,CAACF,IAAI,CAACE,MAAM,CAAgBA,MAAM,CAAC;EAChD;EAEA;EACA;;;;;;;;;;;;;;;;;;;;;;;EAuBAO,GAAGA,CACDC,EAAU,EACVC,IAAA,GAAmB,EAAE,EACrBjD,OAAA,GAII,EAAE;IAYN,OAAO,IAAI,CAACsC,IAAI,CAACS,GAAG,CAACC,EAAE,EAAEC,IAAI,EAAEjD,OAAO,CAAC;EACzC;EAEA;;;;;;;EAOAkD,OAAOA,CAACC,IAAY,EAAEC,IAAA,GAA+B;IAAEC,MAAM,EAAE;EAAE,CAAE;IACjE,OAAO,IAAI,CAACrC,QAAQ,CAACkC,OAAO,CAACC,IAAI,EAAEC,IAAI,CAAC;EAC1C;EAEA;;;EAGAE,WAAWA,CAAA;IACT,OAAO,IAAI,CAACtC,QAAQ,CAACsC,WAAW,EAAE;EACpC;EAEA;;;;;;EAMAC,aAAaA,CAACL,OAAwB;IACpC,OAAO,IAAI,CAAClC,QAAQ,CAACuC,aAAa,CAACL,OAAO,CAAC;EAC7C;EAEA;;;EAGAM,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACxC,QAAQ,CAACwC,iBAAiB,EAAE;EAC1C;EAEcrB,eAAeA,CAAA;;;MAC3B,IAAI,IAAI,CAACT,WAAW,EAAE;QACpB,OAAO,MAAM,IAAI,CAACA,WAAW,EAAE;;MAGjC,MAAM;QAAE+B;MAAI,CAAE,GAAG,MAAM,IAAI,CAACxC,IAAI,CAACyC,UAAU,EAAE;MAE7C,OAAO,CAAAjC,EAAA,IAAAF,EAAA,GAAAkC,IAAI,CAACE,OAAO,cAAApC,EAAA,uBAAAA,EAAA,CAAEqC,YAAY,cAAAnC,EAAA,cAAAA,EAAA,GAAI,IAAI;;;EAGnCE,uBAAuBA,CAC7B;IACEkC,gBAAgB;IAChBC,cAAc;IACdC,kBAAkB;IAClBnB,OAAO;IACPxB,UAAU;IACV4C,QAAQ;IACRC,IAAI;IACJC;EAAK,CACqB,EAC5B1C,OAAgC,EAChCK,KAAa;IAEb,MAAMsC,WAAW,GAAG;MAClBC,aAAa,EAAE,UAAU,IAAI,CAACrE,WAAW,EAAE;MAC3CsE,MAAM,EAAE,GAAG,IAAI,CAACtE,WAAW;KAC5B;IACD,OAAO,IAAIJ,kBAAkB,CAAC;MAC5B2E,GAAG,EAAE,IAAI,CAAC9D,OAAO,CAAC+B,IAAI;MACtBf,OAAO,EAAAN,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAOgD,WAAW,GAAK3C,OAAO,CAAE;MACvCJ,UAAU,EAAEA,UAAU;MACtByC,gBAAgB;MAChBC,cAAc;MACdC,kBAAkB;MAClBnB,OAAO;MACPoB,QAAQ;MACRC,IAAI;MACJC,KAAK;MACLrC,KAAK;MACL;MACA;MACA0C,4BAA4B,EAAE,eAAe,IAAI,IAAI,CAAC/C;KACvD,CAAC;EACJ;EAEQa,mBAAmBA,CAACrC,OAA8B;IACxD,OAAO,IAAIf,cAAc,CAAC,IAAI,CAACoB,WAAW,CAACkC,IAAI,EAAArB,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAC1CnB,OAAO;MACVwE,MAAM,EAAAtD,MAAA,CAAAC,MAAA,CAAO;QAAEkD,MAAM,EAAE,IAAI,CAACtE;MAAW,CAAE,EAAKC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEwE,MAAM;IAAA,GAC7D;EACJ;EAEQ/B,oBAAoBA,CAAA;IAC1B,IAAIgB,IAAI,GAAG,IAAI,CAACxC,IAAI,CAACwD,iBAAiB,CAAC,CAACC,KAAK,EAAEf,OAAO,KAAI;MACxD,IAAI,CAACgB,mBAAmB,CAACD,KAAK,EAAE,QAAQ,EAAEf,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEC,YAAY,CAAC;IAClE,CAAC,CAAC;IACF,OAAOH,IAAI;EACb;EAEQkB,mBAAmBA,CACzBD,KAAsB,EACtBE,MAA4B,EAC5BC,KAAc;IAEd,IACE,CAACH,KAAK,KAAK,iBAAiB,IAAIA,KAAK,KAAK,WAAW,KACrD,IAAI,CAACI,kBAAkB,KAAKD,KAAK,EACjC;MACA,IAAI,CAACC,kBAAkB,GAAGD,KAAK;KAChC,MAAM,IAAIH,KAAK,KAAK,YAAY,EAAE;MACjC,IAAI,CAAC1D,QAAQ,CAAC+D,OAAO,EAAE;MACvB,IAAIH,MAAM,IAAI,SAAS,EAAE,IAAI,CAAC3D,IAAI,CAAC+D,OAAO,EAAE;MAC5C,IAAI,CAACF,kBAAkB,GAAGG,SAAS;;EAEvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}