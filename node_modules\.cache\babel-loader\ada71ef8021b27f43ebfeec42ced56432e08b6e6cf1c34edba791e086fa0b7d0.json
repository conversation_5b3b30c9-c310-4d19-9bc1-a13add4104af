{"ast": null, "code": "import SupabaseClient from './SupabaseClient';\nexport * from '@supabase/auth-js';\nexport { PostgrestError } from '@supabase/postgrest-js';\nexport { FunctionsHttpError, FunctionsFetchError, FunctionsRelayError, FunctionsError, FunctionRegion } from '@supabase/functions-js';\nexport * from '@supabase/realtime-js';\nexport { default as SupabaseClient } from './SupabaseClient';\n/**\n * Creates a new Supabase Client.\n */\nexport const createClient = (supabaseUrl, supabaseKey, options) => {\n  return new SupabaseClient(supabaseUrl, supabaseKey, options);\n};", "map": {"version": 3, "names": ["SupabaseClient", "PostgrestError", "FunctionsHttpError", "FunctionsFetchError", "FunctionsRelayError", "FunctionsError", "FunctionRegion", "default", "createClient", "supabaseUrl", "supabase<PERSON>ey", "options"], "sources": ["D:\\GrowthSch\\IGCSEStuGuide\\node_modules\\@supabase\\supabase-js\\src\\index.ts"], "sourcesContent": ["import SupabaseClient from './SupabaseClient'\nimport type { GenericSchema, SupabaseClientOptions } from './lib/types'\n\nexport * from '@supabase/auth-js'\nexport type { User as AuthUser, Session as AuthSession } from '@supabase/auth-js'\nexport {\n  type PostgrestResponse,\n  type PostgrestSingleResponse,\n  type PostgrestMaybeSingleResponse,\n  PostgrestError,\n} from '@supabase/postgrest-js'\nexport {\n  FunctionsHttpError,\n  FunctionsFetchError,\n  FunctionsRelayError,\n  FunctionsError,\n  type FunctionInvokeOptions,\n  FunctionRegion,\n} from '@supabase/functions-js'\nexport * from '@supabase/realtime-js'\nexport { default as SupabaseClient } from './SupabaseClient'\nexport type { SupabaseClientOptions, QueryResult, QueryData, QueryError } from './lib/types'\n\n/**\n * Creates a new Supabase Client.\n */\nexport const createClient = <\n  Database = any,\n  SchemaName extends string & keyof Database = 'public' extends keyof Database\n    ? 'public'\n    : string & keyof Database,\n  Schema extends GenericSchema = Database[SchemaName] extends GenericSchema\n    ? Database[SchemaName]\n    : any\n>(\n  supabaseUrl: string,\n  supabaseKey: string,\n  options?: SupabaseClientOptions<SchemaName>\n): SupabaseClient<Database, SchemaName, Schema> => {\n  return new SupabaseClient<Database, SchemaName, Schema>(supabaseUrl, supabaseKey, options)\n}\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,kBAAkB;AAG7C,cAAc,mBAAmB;AAEjC,SAIEC,cAAc,QACT,wBAAwB;AAC/B,SACEC,kBAAkB,EAClBC,mBAAmB,EACnBC,mBAAmB,EACnBC,cAAc,EAEdC,cAAc,QACT,wBAAwB;AAC/B,cAAc,uBAAuB;AACrC,SAASC,OAAO,IAAIP,cAAc,QAAQ,kBAAkB;AAG5D;;;AAGA,OAAO,MAAMQ,YAAY,GAAGA,CAS1BC,WAAmB,EACnBC,WAAmB,EACnBC,OAA2C,KACK;EAChD,OAAO,IAAIX,cAAc,CAA+BS,WAAW,EAAEC,WAAW,EAAEC,OAAO,CAAC;AAC5F,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}