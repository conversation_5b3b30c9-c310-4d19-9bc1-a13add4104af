import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

const Navbar: React.FC = () => {
  const { user, signOut } = useAuth();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <nav className="bg-primary-600 text-white shadow-md">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center py-4">
          <Link to="/" className="font-display text-xl font-bold">
            IGCSE Guide
          </Link>
          
          {/* Mobile menu button */}
          <button 
            className="md:hidden"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              {isMenuOpen ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              )}
            </svg>
          </button>
          
          {/* Desktop menu */}
          <div className="hidden md:flex items-center space-x-6">
            <Link to="/subjects" className="hover:text-primary-100">Subjects</Link>
            <Link to="/flashcards" className="hover:text-primary-100">Flashcards</Link>
            <Link to="/quizzes" className="hover:text-primary-100">Quizzes</Link>
            {user ? (
              <>
                <Link to="/dashboard" className="hover:text-primary-100">Dashboard</Link>
                <button 
                  onClick={signOut}
                  className="bg-primary-700 hover:bg-primary-800 px-4 py-2 rounded-md"
                >
                  Sign Out
                </button>
              </>
            ) : (
              <Link 
                to="/login" 
                className="bg-secondary-500 hover:bg-secondary-600 px-4 py-2 rounded-md"
              >
                Sign In
              </Link>
            )}
          </div>
        </div>
        
        {/* Mobile menu */}
        {isMenuOpen && (
          <div className="md:hidden py-4 space-y-3">
            <Link to="/subjects" className="block hover:text-primary-100">Subjects</Link>
            <Link to="/flashcards" className="block hover:text-primary-100">Flashcards</Link>
            <Link to="/quizzes" className="block hover:text-primary-100">Quizzes</Link>
            {user ? (
              <>
                <Link to="/dashboard" className="block hover:text-primary-100">Dashboard</Link>
                <button 
                  onClick={signOut}
                  className="block w-full text-left bg-primary-700 hover:bg-primary-800 px-4 py-2 rounded-md"
                >
                  Sign Out
                </button>
              </>
            ) : (
              <Link 
                to="/login" 
                className="block w-full text-center bg-secondary-500 hover:bg-secondary-600 px-4 py-2 rounded-md"
              >
                Sign In
              </Link>
            )}
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navbar;