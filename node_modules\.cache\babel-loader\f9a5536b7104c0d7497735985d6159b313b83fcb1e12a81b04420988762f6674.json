{"ast": null, "code": "var _jsxFileName = \"D:\\\\GrowthSch\\\\IGCSEStuGuide\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoginForm = () => {\n  _s();\n  const [email, setEmail] = useState('');\n  const [message, setMessage] = useState(null);\n  const {\n    signInWithMagicLink,\n    isLoading\n  } = useAuth();\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!email.trim()) {\n      setMessage({\n        text: 'Please enter your email address',\n        type: 'error'\n      });\n      return;\n    }\n    const {\n      error\n    } = await signInWithMagicLink(email);\n    if (error) {\n      setMessage({\n        text: error.message,\n        type: 'error'\n      });\n    } else {\n      setMessage({\n        text: 'Check your email for the magic link!',\n        type: 'success'\n      });\n      setEmail('');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-md mx-auto bg-white p-8 rounded-lg shadow-md\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-2xl font-display font-semibold text-center mb-6\",\n      children: \"Sign In\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `p-4 mb-6 rounded-md ${message.type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n      children: message.text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"email\",\n          className: \"block text-sm font-medium text-neutral-700 mb-2\",\n          children: \"Email Address\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          id: \"email\",\n          type: \"email\",\n          value: email,\n          onChange: e => setEmail(e.target.value),\n          className: \"w-full px-4 py-2 border border-neutral-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500\",\n          placeholder: \"<EMAIL>\",\n          disabled: isLoading\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        disabled: isLoading,\n        className: \"w-full bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out disabled:opacity-70\",\n        children: isLoading ? 'Sending...' : 'Send Magic Link'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"mt-6 text-center text-sm text-neutral-600\",\n      children: [\"We'll send you a magic link to your email.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), \"No password required!\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this);\n};\n_s(LoginForm, \"OfozrnoDQFQ+iyB5FjidNTIqQmc=\", false, function () {\n  return [useAuth];\n});\n_c = LoginForm;\nexport default LoginForm;\nvar _c;\n$RefreshReg$(_c, \"LoginForm\");", "map": {"version": 3, "names": ["React", "useState", "useAuth", "jsxDEV", "_jsxDEV", "LoginForm", "_s", "email", "setEmail", "message", "setMessage", "signInWithMagicLink", "isLoading", "handleSubmit", "e", "preventDefault", "trim", "text", "type", "error", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "id", "value", "onChange", "target", "placeholder", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/GrowthSch/IGCSEStuGuide/src/components/auth/LoginForm.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useAuth } from '../../contexts/AuthContext';\n\nconst LoginForm: React.FC = () => {\n  const [email, setEmail] = useState('');\n  const [message, setMessage] = useState<{ text: string; type: 'success' | 'error' } | null>(null);\n  const { signInWithMagicLink, isLoading } = useAuth();\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!email.trim()) {\n      setMessage({ text: 'Please enter your email address', type: 'error' });\n      return;\n    }\n    \n    const { error } = await signInWithMagicLink(email);\n    \n    if (error) {\n      setMessage({ text: error.message, type: 'error' });\n    } else {\n      setMessage({ \n        text: 'Check your email for the magic link!', \n        type: 'success' \n      });\n      setEmail('');\n    }\n  };\n\n  return (\n    <div className=\"max-w-md mx-auto bg-white p-8 rounded-lg shadow-md\">\n      <h2 className=\"text-2xl font-display font-semibold text-center mb-6\">Sign In</h2>\n      \n      {message && (\n        <div className={`p-4 mb-6 rounded-md ${\n          message.type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'\n        }`}>\n          {message.text}\n        </div>\n      )}\n      \n      <form onSubmit={handleSubmit}>\n        <div className=\"mb-6\">\n          <label htmlFor=\"email\" className=\"block text-sm font-medium text-neutral-700 mb-2\">\n            Email Address\n          </label>\n          <input\n            id=\"email\"\n            type=\"email\"\n            value={email}\n            onChange={(e) => setEmail(e.target.value)}\n            className=\"w-full px-4 py-2 border border-neutral-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500\"\n            placeholder=\"<EMAIL>\"\n            disabled={isLoading}\n          />\n        </div>\n        \n        <button\n          type=\"submit\"\n          disabled={isLoading}\n          className=\"w-full bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out disabled:opacity-70\"\n        >\n          {isLoading ? 'Sending...' : 'Send Magic Link'}\n        </button>\n      </form>\n      \n      <p className=\"mt-6 text-center text-sm text-neutral-600\">\n        We'll send you a magic link to your email.\n        <br />\n        No password required!\n      </p>\n    </div>\n  );\n};\n\nexport default LoginForm;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,OAAO,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACQ,OAAO,EAAEC,UAAU,CAAC,GAAGT,QAAQ,CAAqD,IAAI,CAAC;EAChG,MAAM;IAAEU,mBAAmB;IAAEC;EAAU,CAAC,GAAGV,OAAO,CAAC,CAAC;EAEpD,MAAMW,YAAY,GAAG,MAAOC,CAAkB,IAAK;IACjDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAACR,KAAK,CAACS,IAAI,CAAC,CAAC,EAAE;MACjBN,UAAU,CAAC;QAAEO,IAAI,EAAE,iCAAiC;QAAEC,IAAI,EAAE;MAAQ,CAAC,CAAC;MACtE;IACF;IAEA,MAAM;MAAEC;IAAM,CAAC,GAAG,MAAMR,mBAAmB,CAACJ,KAAK,CAAC;IAElD,IAAIY,KAAK,EAAE;MACTT,UAAU,CAAC;QAAEO,IAAI,EAAEE,KAAK,CAACV,OAAO;QAAES,IAAI,EAAE;MAAQ,CAAC,CAAC;IACpD,CAAC,MAAM;MACLR,UAAU,CAAC;QACTO,IAAI,EAAE,sCAAsC;QAC5CC,IAAI,EAAE;MACR,CAAC,CAAC;MACFV,QAAQ,CAAC,EAAE,CAAC;IACd;EACF,CAAC;EAED,oBACEJ,OAAA;IAAKgB,SAAS,EAAC,oDAAoD;IAAAC,QAAA,gBACjEjB,OAAA;MAAIgB,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAAC;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAEhFhB,OAAO,iBACNL,OAAA;MAAKgB,SAAS,EAAE,uBACdX,OAAO,CAACS,IAAI,KAAK,SAAS,GAAG,6BAA6B,GAAG,yBAAyB,EACrF;MAAAG,QAAA,EACAZ,OAAO,CAACQ;IAAI;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACN,eAEDrB,OAAA;MAAMsB,QAAQ,EAAEb,YAAa;MAAAQ,QAAA,gBAC3BjB,OAAA;QAAKgB,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBjB,OAAA;UAAOuB,OAAO,EAAC,OAAO;UAACP,SAAS,EAAC,iDAAiD;UAAAC,QAAA,EAAC;QAEnF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRrB,OAAA;UACEwB,EAAE,EAAC,OAAO;UACVV,IAAI,EAAC,OAAO;UACZW,KAAK,EAAEtB,KAAM;UACbuB,QAAQ,EAAGhB,CAAC,IAAKN,QAAQ,CAACM,CAAC,CAACiB,MAAM,CAACF,KAAK,CAAE;UAC1CT,SAAS,EAAC,oHAAoH;UAC9HY,WAAW,EAAC,wBAAwB;UACpCC,QAAQ,EAAErB;QAAU;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENrB,OAAA;QACEc,IAAI,EAAC,QAAQ;QACbe,QAAQ,EAAErB,SAAU;QACpBQ,SAAS,EAAC,gJAAgJ;QAAAC,QAAA,EAEzJT,SAAS,GAAG,YAAY,GAAG;MAAiB;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEPrB,OAAA;MAAGgB,SAAS,EAAC,2CAA2C;MAAAC,QAAA,GAAC,4CAEvD,eAAAjB,OAAA;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,yBAER;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEV,CAAC;AAACnB,EAAA,CAtEID,SAAmB;EAAA,QAGoBH,OAAO;AAAA;AAAAgC,EAAA,GAH9C7B,SAAmB;AAwEzB,eAAeA,SAAS;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}