{"ast": null, "code": "import { supportsLocalStorage } from './helpers';\n/**\n * @experimental\n */\nexport const internals = {\n  /**\n   * @experimental\n   */\n  debug: !!(globalThis && supportsLocalStorage() && globalThis.localStorage && globalThis.localStorage.getItem('supabase.gotrue-js.locks.debug') === 'true')\n};\n/**\n * An error thrown when a lock cannot be acquired after some amount of time.\n *\n * Use the {@link #isAcquireTimeout} property instead of checking with `instanceof`.\n */\nexport class LockAcquireTimeoutError extends Error {\n  constructor(message) {\n    super(message);\n    this.isAcquireTimeout = true;\n  }\n}\nexport class NavigatorLockAcquireTimeoutError extends LockAcquireTimeoutError {}\nexport class ProcessLockAcquireTimeoutError extends LockAcquireTimeoutError {}\n/**\n * Implements a global exclusive lock using the Navigator LockManager API. It\n * is available on all browsers released after 2022-03-15 with <PERSON><PERSON> being the\n * last one to release support. If the API is not available, this function will\n * throw. Make sure you check availablility before configuring {@link\n * GoTrueClient}.\n *\n * You can turn on debugging by setting the `supabase.gotrue-js.locks.debug`\n * local storage item to `true`.\n *\n * Internals:\n *\n * Since the LockManager API does not preserve stack traces for the async\n * function passed in the `request` method, a trick is used where acquiring the\n * lock releases a previously started promise to run the operation in the `fn`\n * function. The lock waits for that promise to finish (with or without error),\n * while the function will finally wait for the result anyway.\n *\n * @param name Name of the lock to be acquired.\n * @param acquireTimeout If negative, no timeout. If 0 an error is thrown if\n *                       the lock can't be acquired without waiting. If positive, the lock acquire\n *                       will time out after so many milliseconds. An error is\n *                       a timeout if it has `isAcquireTimeout` set to true.\n * @param fn The operation to run once the lock is acquired.\n */\nexport async function navigatorLock(name, acquireTimeout, fn) {\n  if (internals.debug) {\n    console.log('@supabase/gotrue-js: navigatorLock: acquire lock', name, acquireTimeout);\n  }\n  const abortController = new globalThis.AbortController();\n  if (acquireTimeout > 0) {\n    setTimeout(() => {\n      abortController.abort();\n      if (internals.debug) {\n        console.log('@supabase/gotrue-js: navigatorLock acquire timed out', name);\n      }\n    }, acquireTimeout);\n  }\n  // MDN article: https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request\n  // Wrapping navigator.locks.request() with a plain Promise is done as some\n  // libraries like zone.js patch the Promise object to track the execution\n  // context. However, it appears that most browsers use an internal promise\n  // implementation when using the navigator.locks.request() API causing them\n  // to lose context and emit confusing log messages or break certain features.\n  // This wrapping is believed to help zone.js track the execution context\n  // better.\n  return await Promise.resolve().then(() => globalThis.navigator.locks.request(name, acquireTimeout === 0 ? {\n    mode: 'exclusive',\n    ifAvailable: true\n  } : {\n    mode: 'exclusive',\n    signal: abortController.signal\n  }, async lock => {\n    if (lock) {\n      if (internals.debug) {\n        console.log('@supabase/gotrue-js: navigatorLock: acquired', name, lock.name);\n      }\n      try {\n        return await fn();\n      } finally {\n        if (internals.debug) {\n          console.log('@supabase/gotrue-js: navigatorLock: released', name, lock.name);\n        }\n      }\n    } else {\n      if (acquireTimeout === 0) {\n        if (internals.debug) {\n          console.log('@supabase/gotrue-js: navigatorLock: not immediately available', name);\n        }\n        throw new NavigatorLockAcquireTimeoutError(`Acquiring an exclusive Navigator LockManager lock \"${name}\" immediately failed`);\n      } else {\n        if (internals.debug) {\n          try {\n            const result = await globalThis.navigator.locks.query();\n            console.log('@supabase/gotrue-js: Navigator LockManager state', JSON.stringify(result, null, '  '));\n          } catch (e) {\n            console.warn('@supabase/gotrue-js: Error when querying Navigator LockManager state', e);\n          }\n        }\n        // Browser is not following the Navigator LockManager spec, it\n        // returned a null lock when we didn't use ifAvailable. So we can\n        // pretend the lock is acquired in the name of backward compatibility\n        // and user experience and just run the function.\n        console.warn('@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request');\n        return await fn();\n      }\n    }\n  }));\n}\nconst PROCESS_LOCKS = {};\n/**\n * Implements a global exclusive lock that works only in the current process.\n * Useful for environments like React Native or other non-browser\n * single-process (i.e. no concept of \"tabs\") environments.\n *\n * Use {@link #navigatorLock} in browser environments.\n *\n * @param name Name of the lock to be acquired.\n * @param acquireTimeout If negative, no timeout. If 0 an error is thrown if\n *                       the lock can't be acquired without waiting. If positive, the lock acquire\n *                       will time out after so many milliseconds. An error is\n *                       a timeout if it has `isAcquireTimeout` set to true.\n * @param fn The operation to run once the lock is acquired.\n */\nexport async function processLock(name, acquireTimeout, fn) {\n  var _a;\n  const previousOperation = (_a = PROCESS_LOCKS[name]) !== null && _a !== void 0 ? _a : Promise.resolve();\n  const currentOperation = Promise.race([previousOperation.catch(() => {\n    // ignore error of previous operation that we're waiting to finish\n    return null;\n  }), acquireTimeout >= 0 ? new Promise((_, reject) => {\n    setTimeout(() => {\n      reject(new ProcessLockAcquireTimeoutError(`Acquring process lock with name \"${name}\" timed out`));\n    }, acquireTimeout);\n  }) : null].filter(x => x)).catch(e => {\n    if (e && e.isAcquireTimeout) {\n      throw e;\n    }\n    return null;\n  }).then(async () => {\n    // previous operations finished and we didn't get a race on the acquire\n    // timeout, so the current operation can finally start\n    return await fn();\n  });\n  PROCESS_LOCKS[name] = currentOperation.catch(async e => {\n    if (e && e.isAcquireTimeout) {\n      // if the current operation timed out, it doesn't mean that the previous\n      // operation finished, so we need contnue waiting for it to finish\n      await previousOperation;\n      return null;\n    }\n    throw e;\n  });\n  // finally wait for the current operation to finish successfully, with an\n  // error or with an acquire timeout error\n  return await currentOperation;\n}", "map": {"version": 3, "names": ["supportsLocalStorage", "internals", "debug", "globalThis", "localStorage", "getItem", "LockAcquireTimeoutError", "Error", "constructor", "message", "isAcquireTimeout", "NavigatorLockAcquireTimeoutError", "ProcessLockAcquireTimeoutError", "navigator<PERSON><PERSON>", "name", "acquireTimeout", "fn", "console", "log", "abortController", "AbortController", "setTimeout", "abort", "Promise", "resolve", "then", "navigator", "locks", "request", "mode", "ifAvailable", "signal", "lock", "result", "query", "JSON", "stringify", "e", "warn", "PROCESS_LOCKS", "processLock", "previousOperation", "_a", "currentOperation", "race", "catch", "_", "reject", "filter", "x"], "sources": ["D:\\GrowthSch\\IGCSEStuGuide\\node_modules\\@supabase\\auth-js\\src\\lib\\locks.ts"], "sourcesContent": ["import { supportsLocalStorage } from './helpers'\n\n/**\n * @experimental\n */\nexport const internals = {\n  /**\n   * @experimental\n   */\n  debug: !!(\n    globalThis &&\n    supportsLocalStorage() &&\n    globalThis.localStorage &&\n    globalThis.localStorage.getItem('supabase.gotrue-js.locks.debug') === 'true'\n  ),\n}\n\n/**\n * An error thrown when a lock cannot be acquired after some amount of time.\n *\n * Use the {@link #isAcquireTimeout} property instead of checking with `instanceof`.\n */\nexport abstract class LockAcquireTimeoutError extends Error {\n  public readonly isAcquireTimeout = true\n\n  constructor(message: string) {\n    super(message)\n  }\n}\n\nexport class NavigatorLockAcquireTimeoutError extends LockAcquireTimeoutError {}\nexport class ProcessLockAcquireTimeoutError extends LockAcquireTimeoutError {}\n\n/**\n * Implements a global exclusive lock using the Navigator LockManager API. It\n * is available on all browsers released after 2022-03-15 with <PERSON><PERSON> being the\n * last one to release support. If the API is not available, this function will\n * throw. Make sure you check availablility before configuring {@link\n * GoTrueClient}.\n *\n * You can turn on debugging by setting the `supabase.gotrue-js.locks.debug`\n * local storage item to `true`.\n *\n * Internals:\n *\n * Since the LockManager API does not preserve stack traces for the async\n * function passed in the `request` method, a trick is used where acquiring the\n * lock releases a previously started promise to run the operation in the `fn`\n * function. The lock waits for that promise to finish (with or without error),\n * while the function will finally wait for the result anyway.\n *\n * @param name Name of the lock to be acquired.\n * @param acquireTimeout If negative, no timeout. If 0 an error is thrown if\n *                       the lock can't be acquired without waiting. If positive, the lock acquire\n *                       will time out after so many milliseconds. An error is\n *                       a timeout if it has `isAcquireTimeout` set to true.\n * @param fn The operation to run once the lock is acquired.\n */\nexport async function navigatorLock<R>(\n  name: string,\n  acquireTimeout: number,\n  fn: () => Promise<R>\n): Promise<R> {\n  if (internals.debug) {\n    console.log('@supabase/gotrue-js: navigatorLock: acquire lock', name, acquireTimeout)\n  }\n\n  const abortController = new globalThis.AbortController()\n\n  if (acquireTimeout > 0) {\n    setTimeout(() => {\n      abortController.abort()\n      if (internals.debug) {\n        console.log('@supabase/gotrue-js: navigatorLock acquire timed out', name)\n      }\n    }, acquireTimeout)\n  }\n\n  // MDN article: https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request\n\n  // Wrapping navigator.locks.request() with a plain Promise is done as some\n  // libraries like zone.js patch the Promise object to track the execution\n  // context. However, it appears that most browsers use an internal promise\n  // implementation when using the navigator.locks.request() API causing them\n  // to lose context and emit confusing log messages or break certain features.\n  // This wrapping is believed to help zone.js track the execution context\n  // better.\n  return await Promise.resolve().then(() =>\n    globalThis.navigator.locks.request(\n      name,\n      acquireTimeout === 0\n        ? {\n            mode: 'exclusive',\n            ifAvailable: true,\n          }\n        : {\n            mode: 'exclusive',\n            signal: abortController.signal,\n          },\n      async (lock) => {\n        if (lock) {\n          if (internals.debug) {\n            console.log('@supabase/gotrue-js: navigatorLock: acquired', name, lock.name)\n          }\n\n          try {\n            return await fn()\n          } finally {\n            if (internals.debug) {\n              console.log('@supabase/gotrue-js: navigatorLock: released', name, lock.name)\n            }\n          }\n        } else {\n          if (acquireTimeout === 0) {\n            if (internals.debug) {\n              console.log('@supabase/gotrue-js: navigatorLock: not immediately available', name)\n            }\n\n            throw new NavigatorLockAcquireTimeoutError(\n              `Acquiring an exclusive Navigator LockManager lock \"${name}\" immediately failed`\n            )\n          } else {\n            if (internals.debug) {\n              try {\n                const result = await globalThis.navigator.locks.query()\n\n                console.log(\n                  '@supabase/gotrue-js: Navigator LockManager state',\n                  JSON.stringify(result, null, '  ')\n                )\n              } catch (e: any) {\n                console.warn(\n                  '@supabase/gotrue-js: Error when querying Navigator LockManager state',\n                  e\n                )\n              }\n            }\n\n            // Browser is not following the Navigator LockManager spec, it\n            // returned a null lock when we didn't use ifAvailable. So we can\n            // pretend the lock is acquired in the name of backward compatibility\n            // and user experience and just run the function.\n            console.warn(\n              '@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request'\n            )\n\n            return await fn()\n          }\n        }\n      }\n    )\n  )\n}\n\nconst PROCESS_LOCKS: { [name: string]: Promise<any> } = {}\n\n/**\n * Implements a global exclusive lock that works only in the current process.\n * Useful for environments like React Native or other non-browser\n * single-process (i.e. no concept of \"tabs\") environments.\n *\n * Use {@link #navigatorLock} in browser environments.\n *\n * @param name Name of the lock to be acquired.\n * @param acquireTimeout If negative, no timeout. If 0 an error is thrown if\n *                       the lock can't be acquired without waiting. If positive, the lock acquire\n *                       will time out after so many milliseconds. An error is\n *                       a timeout if it has `isAcquireTimeout` set to true.\n * @param fn The operation to run once the lock is acquired.\n */\nexport async function processLock<R>(\n  name: string,\n  acquireTimeout: number,\n  fn: () => Promise<R>\n): Promise<R> {\n  const previousOperation = PROCESS_LOCKS[name] ?? Promise.resolve()\n\n  const currentOperation = Promise.race(\n    [\n      previousOperation.catch(() => {\n        // ignore error of previous operation that we're waiting to finish\n        return null\n      }),\n      acquireTimeout >= 0\n        ? new Promise((_, reject) => {\n            setTimeout(() => {\n              reject(\n                new ProcessLockAcquireTimeoutError(\n                  `Acquring process lock with name \"${name}\" timed out`\n                )\n              )\n            }, acquireTimeout)\n          })\n        : null,\n    ].filter((x) => x)\n  )\n    .catch((e: any) => {\n      if (e && e.isAcquireTimeout) {\n        throw e\n      }\n\n      return null\n    })\n    .then(async () => {\n      // previous operations finished and we didn't get a race on the acquire\n      // timeout, so the current operation can finally start\n      return await fn()\n    })\n\n  PROCESS_LOCKS[name] = currentOperation.catch(async (e: any) => {\n    if (e && e.isAcquireTimeout) {\n      // if the current operation timed out, it doesn't mean that the previous\n      // operation finished, so we need contnue waiting for it to finish\n      await previousOperation\n\n      return null\n    }\n\n    throw e\n  })\n\n  // finally wait for the current operation to finish successfully, with an\n  // error or with an acquire timeout error\n  return await currentOperation\n}\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,WAAW;AAEhD;;;AAGA,OAAO,MAAMC,SAAS,GAAG;EACvB;;;EAGAC,KAAK,EAAE,CAAC,EACNC,UAAU,IACVH,oBAAoB,EAAE,IACtBG,UAAU,CAACC,YAAY,IACvBD,UAAU,CAACC,YAAY,CAACC,OAAO,CAAC,gCAAgC,CAAC,KAAK,MAAM;CAE/E;AAED;;;;;AAKA,OAAM,MAAgBC,uBAAwB,SAAQC,KAAK;EAGzDC,YAAYC,OAAe;IACzB,KAAK,CAACA,OAAO,CAAC;IAHA,KAAAC,gBAAgB,GAAG,IAAI;EAIvC;;AAGF,OAAM,MAAOC,gCAAiC,SAAQL,uBAAuB;AAC7E,OAAM,MAAOM,8BAA+B,SAAQN,uBAAuB;AAE3E;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,OAAO,eAAeO,aAAaA,CACjCC,IAAY,EACZC,cAAsB,EACtBC,EAAoB;EAEpB,IAAIf,SAAS,CAACC,KAAK,EAAE;IACnBe,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAEJ,IAAI,EAAEC,cAAc,CAAC;;EAGvF,MAAMI,eAAe,GAAG,IAAIhB,UAAU,CAACiB,eAAe,EAAE;EAExD,IAAIL,cAAc,GAAG,CAAC,EAAE;IACtBM,UAAU,CAAC,MAAK;MACdF,eAAe,CAACG,KAAK,EAAE;MACvB,IAAIrB,SAAS,CAACC,KAAK,EAAE;QACnBe,OAAO,CAACC,GAAG,CAAC,sDAAsD,EAAEJ,IAAI,CAAC;;IAE7E,CAAC,EAAEC,cAAc,CAAC;;EAGpB;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,OAAO,MAAMQ,OAAO,CAACC,OAAO,EAAE,CAACC,IAAI,CAAC,MAClCtB,UAAU,CAACuB,SAAS,CAACC,KAAK,CAACC,OAAO,CAChCd,IAAI,EACJC,cAAc,KAAK,CAAC,GAChB;IACEc,IAAI,EAAE,WAAW;IACjBC,WAAW,EAAE;GACd,GACD;IACED,IAAI,EAAE,WAAW;IACjBE,MAAM,EAAEZ,eAAe,CAACY;GACzB,EACL,MAAOC,IAAI,IAAI;IACb,IAAIA,IAAI,EAAE;MACR,IAAI/B,SAAS,CAACC,KAAK,EAAE;QACnBe,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEJ,IAAI,EAAEkB,IAAI,CAAClB,IAAI,CAAC;;MAG9E,IAAI;QACF,OAAO,MAAME,EAAE,EAAE;OAClB,SAAS;QACR,IAAIf,SAAS,CAACC,KAAK,EAAE;UACnBe,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEJ,IAAI,EAAEkB,IAAI,CAAClB,IAAI,CAAC;;;KAGjF,MAAM;MACL,IAAIC,cAAc,KAAK,CAAC,EAAE;QACxB,IAAId,SAAS,CAACC,KAAK,EAAE;UACnBe,OAAO,CAACC,GAAG,CAAC,+DAA+D,EAAEJ,IAAI,CAAC;;QAGpF,MAAM,IAAIH,gCAAgC,CACxC,sDAAsDG,IAAI,sBAAsB,CACjF;OACF,MAAM;QACL,IAAIb,SAAS,CAACC,KAAK,EAAE;UACnB,IAAI;YACF,MAAM+B,MAAM,GAAG,MAAM9B,UAAU,CAACuB,SAAS,CAACC,KAAK,CAACO,KAAK,EAAE;YAEvDjB,OAAO,CAACC,GAAG,CACT,kDAAkD,EAClDiB,IAAI,CAACC,SAAS,CAACH,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CACnC;WACF,CAAC,OAAOI,CAAM,EAAE;YACfpB,OAAO,CAACqB,IAAI,CACV,sEAAsE,EACtED,CAAC,CACF;;;QAIL;QACA;QACA;QACA;QACApB,OAAO,CAACqB,IAAI,CACV,yPAAyP,CAC1P;QAED,OAAO,MAAMtB,EAAE,EAAE;;;EAGvB,CAAC,CACF,CACF;AACH;AAEA,MAAMuB,aAAa,GAAqC,EAAE;AAE1D;;;;;;;;;;;;;;AAcA,OAAO,eAAeC,WAAWA,CAC/B1B,IAAY,EACZC,cAAsB,EACtBC,EAAoB;;EAEpB,MAAMyB,iBAAiB,GAAG,CAAAC,EAAA,GAAAH,aAAa,CAACzB,IAAI,CAAC,cAAA4B,EAAA,cAAAA,EAAA,GAAInB,OAAO,CAACC,OAAO,EAAE;EAElE,MAAMmB,gBAAgB,GAAGpB,OAAO,CAACqB,IAAI,CACnC,CACEH,iBAAiB,CAACI,KAAK,CAAC,MAAK;IAC3B;IACA,OAAO,IAAI;EACb,CAAC,CAAC,EACF9B,cAAc,IAAI,CAAC,GACf,IAAIQ,OAAO,CAAC,CAACuB,CAAC,EAAEC,MAAM,KAAI;IACxB1B,UAAU,CAAC,MAAK;MACd0B,MAAM,CACJ,IAAInC,8BAA8B,CAChC,oCAAoCE,IAAI,aAAa,CACtD,CACF;IACH,CAAC,EAAEC,cAAc,CAAC;EACpB,CAAC,CAAC,GACF,IAAI,CACT,CAACiC,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAAC,CACnB,CACEJ,KAAK,CAAER,CAAM,IAAI;IAChB,IAAIA,CAAC,IAAIA,CAAC,CAAC3B,gBAAgB,EAAE;MAC3B,MAAM2B,CAAC;;IAGT,OAAO,IAAI;EACb,CAAC,CAAC,CACDZ,IAAI,CAAC,YAAW;IACf;IACA;IACA,OAAO,MAAMT,EAAE,EAAE;EACnB,CAAC,CAAC;EAEJuB,aAAa,CAACzB,IAAI,CAAC,GAAG6B,gBAAgB,CAACE,KAAK,CAAC,MAAOR,CAAM,IAAI;IAC5D,IAAIA,CAAC,IAAIA,CAAC,CAAC3B,gBAAgB,EAAE;MAC3B;MACA;MACA,MAAM+B,iBAAiB;MAEvB,OAAO,IAAI;;IAGb,MAAMJ,CAAC;EACT,CAAC,CAAC;EAEF;EACA;EACA,OAAO,MAAMM,gBAAgB;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}