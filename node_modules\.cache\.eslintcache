[{"D:\\GrowthSch\\IGCSEStuGuide\\src\\index.tsx": "1", "D:\\GrowthSch\\IGCSEStuGuide\\src\\App.tsx": "2", "D:\\GrowthSch\\IGCSEStuGuide\\src\\pages\\DashboardPage.tsx": "3", "D:\\GrowthSch\\IGCSEStuGuide\\src\\contexts\\AuthContext.tsx": "4", "D:\\GrowthSch\\IGCSEStuGuide\\src\\pages\\LoginPage.tsx": "5", "D:\\GrowthSch\\IGCSEStuGuide\\src\\pages\\HomePage.tsx": "6", "D:\\GrowthSch\\IGCSEStuGuide\\src\\pages\\AuthCallback.tsx": "7", "D:\\GrowthSch\\IGCSEStuGuide\\src\\pages\\SubjectsPage.tsx": "8", "D:\\GrowthSch\\IGCSEStuGuide\\src\\pages\\FlashcardsPage.tsx": "9", "D:\\GrowthSch\\IGCSEStuGuide\\src\\pages\\QuizzesPage.tsx": "10", "D:\\GrowthSch\\IGCSEStuGuide\\src\\components\\layout\\AppShell.tsx": "11", "D:\\GrowthSch\\IGCSEStuGuide\\src\\lib\\supabase.ts": "12", "D:\\GrowthSch\\IGCSEStuGuide\\src\\components\\auth\\LoginForm.tsx": "13", "D:\\GrowthSch\\IGCSEStuGuide\\src\\components\\layout\\Footer.tsx": "14", "D:\\GrowthSch\\IGCSEStuGuide\\src\\components\\layout\\Navbar.tsx": "15"}, {"size": 274, "mtime": 1750257256110, "results": "16", "hashOfConfig": "17"}, {"size": 2249, "mtime": 1750253415797, "results": "18", "hashOfConfig": "17"}, {"size": 3731, "mtime": 1750258109602, "results": "19", "hashOfConfig": "17"}, {"size": 2041, "mtime": 1750253059217, "results": "20", "hashOfConfig": "17"}, {"size": 2965, "mtime": 1750253424823, "results": "21", "hashOfConfig": "17"}, {"size": 4139, "mtime": 1750253430064, "results": "22", "hashOfConfig": "17"}, {"size": 1502, "mtime": 1750253075255, "results": "23", "hashOfConfig": "17"}, {"size": 2172, "mtime": 1750257270760, "results": "24", "hashOfConfig": "17"}, {"size": 3476, "mtime": 1750257287183, "results": "25", "hashOfConfig": "17"}, {"size": 5017, "mtime": 1750257313114, "results": "26", "hashOfConfig": "17"}, {"size": 444, "mtime": 1750253036746, "results": "27", "hashOfConfig": "17"}, {"size": 358, "mtime": 1750253025903, "results": "28", "hashOfConfig": "17"}, {"size": 2416, "mtime": 1750253067424, "results": "29", "hashOfConfig": "17"}, {"size": 1626, "mtime": 1750253051711, "results": "30", "hashOfConfig": "17"}, {"size": 3264, "mtime": 1750253041840, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "v4j4ju", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\GrowthSch\\IGCSEStuGuide\\src\\index.tsx", [], [], "D:\\GrowthSch\\IGCSEStuGuide\\src\\App.tsx", [], [], "D:\\GrowthSch\\IGCSEStuGuide\\src\\pages\\DashboardPage.tsx", [], [], "D:\\GrowthSch\\IGCSEStuGuide\\src\\contexts\\AuthContext.tsx", [], [], "D:\\GrowthSch\\IGCSEStuGuide\\src\\pages\\LoginPage.tsx", [], [], "D:\\GrowthSch\\IGCSEStuGuide\\src\\pages\\HomePage.tsx", [], [], "D:\\GrowthSch\\IGCSEStuGuide\\src\\pages\\AuthCallback.tsx", [], [], "D:\\GrowthSch\\IGCSEStuGuide\\src\\pages\\SubjectsPage.tsx", [], [], "D:\\GrowthSch\\IGCSEStuGuide\\src\\pages\\FlashcardsPage.tsx", [], [], "D:\\GrowthSch\\IGCSEStuGuide\\src\\pages\\QuizzesPage.tsx", [], [], "D:\\GrowthSch\\IGCSEStuGuide\\src\\components\\layout\\AppShell.tsx", [], [], "D:\\GrowthSch\\IGCSEStuGuide\\src\\lib\\supabase.ts", [], [], "D:\\GrowthSch\\IGCSEStuGuide\\src\\components\\auth\\LoginForm.tsx", [], [], "D:\\GrowthSch\\IGCSEStuGuide\\src\\components\\layout\\Footer.tsx", [], [], "D:\\GrowthSch\\IGCSEStuGuide\\src\\components\\layout\\Navbar.tsx", [], []]