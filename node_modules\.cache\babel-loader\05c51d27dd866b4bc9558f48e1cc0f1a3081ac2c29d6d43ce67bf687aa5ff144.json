{"ast": null, "code": "var _jsxFileName = \"D:\\\\GrowthSch\\\\IGCSEStuGuide\\\\src\\\\components\\\\layout\\\\Footer.tsx\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Footer = () => {\n  return /*#__PURE__*/_jsxDEV(\"footer\", {\n    className: \"bg-neutral-800 text-neutral-100 py-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold mb-4\",\n            children: \"IGCSE Student Guide\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 10,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-neutral-300\",\n            children: \"A comprehensive learning platform for IGCSE Grade 9-10 students.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 11,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 9,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-lg font-semibold mb-4\",\n            children: \"Quick Links\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/subjects\",\n                className: \"hover:text-white\",\n                children: \"Subjects\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 18,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 18,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/flashcards\",\n                className: \"hover:text-white\",\n                children: \"Flashcards\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 19,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 19,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/quizzes\",\n                className: \"hover:text-white\",\n                children: \"Quizzes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 20,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 20,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-lg font-semibold mb-4\",\n            children: \"Contact\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-neutral-300\",\n            children: [\"Have questions or feedback? \", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 43\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"mailto:<EMAIL>\",\n              className: \"text-secondary-400 hover:text-secondary-300\",\n              children: \"<EMAIL>\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border-t border-neutral-700 mt-8 pt-4 text-center text-neutral-400\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"\\xA9 \", new Date().getFullYear(), \" IGCSE Student Guide. All rights reserved.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = Footer;\nexport default Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "Footer", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "href", "Date", "getFullYear", "_c", "$RefreshReg$"], "sources": ["D:/GrowthSch/IGCSEStuGuide/src/components/layout/Footer.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\nconst Footer: React.FC = () => {\n  return (\n    <footer className=\"bg-neutral-800 text-neutral-100 py-8\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">IGCSE Student Guide</h3>\n            <p className=\"text-neutral-300\">\n              A comprehensive learning platform for IGCSE Grade 9-10 students.\n            </p>\n          </div>\n          <div>\n            <h4 className=\"text-lg font-semibold mb-4\">Quick Links</h4>\n            <ul className=\"space-y-2\">\n              <li><Link to=\"/subjects\" className=\"hover:text-white\">Subjects</Link></li>\n              <li><Link to=\"/flashcards\" className=\"hover:text-white\">Flashcards</Link></li>\n              <li><Link to=\"/quizzes\" className=\"hover:text-white\">Quizzes</Link></li>\n            </ul>\n          </div>\n          <div>\n            <h4 className=\"text-lg font-semibold mb-4\">Contact</h4>\n            <p className=\"text-neutral-300\">\n              Have questions or feedback? <br />\n              <a href=\"mailto:<EMAIL>\" className=\"text-secondary-400 hover:text-secondary-300\">\n                <EMAIL>\n              </a>\n            </p>\n          </div>\n        </div>\n        <div className=\"border-t border-neutral-700 mt-8 pt-4 text-center text-neutral-400\">\n          <p>&copy; {new Date().getFullYear()} IGCSE Student Guide. All rights reserved.</p>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,MAAgB,GAAGA,CAAA,KAAM;EAC7B,oBACED,OAAA;IAAQE,SAAS,EAAC,sCAAsC;IAAAC,QAAA,eACtDH,OAAA;MAAKE,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACrCH,OAAA;QAAKE,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpDH,OAAA;UAAAG,QAAA,gBACEH,OAAA;YAAIE,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnEP,OAAA;YAAGE,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAEhC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNP,OAAA;UAAAG,QAAA,gBACEH,OAAA;YAAIE,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3DP,OAAA;YAAIE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACvBH,OAAA;cAAAG,QAAA,eAAIH,OAAA,CAACF,IAAI;gBAACU,EAAE,EAAC,WAAW;gBAACN,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1EP,OAAA;cAAAG,QAAA,eAAIH,OAAA,CAACF,IAAI;gBAACU,EAAE,EAAC,aAAa;gBAACN,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9EP,OAAA;cAAAG,QAAA,eAAIH,OAAA,CAACF,IAAI;gBAACU,EAAE,EAAC,UAAU;gBAACN,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACNP,OAAA;UAAAG,QAAA,gBACEH,OAAA;YAAIE,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvDP,OAAA;YAAGE,SAAS,EAAC,kBAAkB;YAAAC,QAAA,GAAC,8BACF,eAAAH,OAAA;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAClCP,OAAA;cAAGS,IAAI,EAAC,+BAA+B;cAACP,SAAS,EAAC,6CAA6C;cAAAC,QAAA,EAAC;YAEhG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNP,OAAA;QAAKE,SAAS,EAAC,oEAAoE;QAAAC,QAAA,eACjFH,OAAA;UAAAG,QAAA,GAAG,OAAO,EAAC,IAAIO,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,EAAC,4CAA0C;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACK,EAAA,GAnCIX,MAAgB;AAqCtB,eAAeA,MAAM;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}