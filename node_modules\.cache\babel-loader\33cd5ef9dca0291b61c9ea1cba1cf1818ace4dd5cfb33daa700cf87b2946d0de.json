{"ast": null, "code": "var _jsxFileName = \"D:\\\\GrowthSch\\\\IGCSEStuGuide\\\\src\\\\pages\\\\SubjectsPage.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SubjectsPage = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Subjects\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-neutral-600\",\n        children: \"Explore IGCSE subjects and their comprehensive study materials.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n      children: [{\n        name: 'Mathematics',\n        topics: 12,\n        color: 'bg-blue-500'\n      }, {\n        name: 'Physics',\n        topics: 10,\n        color: 'bg-green-500'\n      }, {\n        name: 'Chemistry',\n        topics: 11,\n        color: 'bg-purple-500'\n      }, {\n        name: 'Biology',\n        topics: 9,\n        color: 'bg-red-500'\n      }, {\n        name: 'English Language',\n        topics: 8,\n        color: 'bg-yellow-500'\n      }, {\n        name: 'History',\n        topics: 7,\n        color: 'bg-indigo-500'\n      }].map(subject => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `w-12 h-12 ${subject.color} rounded-lg mb-4 flex items-center justify-center`,\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-6 h-6 text-white\",\n            fill: \"currentColor\",\n            viewBox: \"0 0 20 20\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              d: \"M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold mb-2\",\n          children: subject.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-neutral-600 mb-4\",\n          children: [subject.topics, \" topics available\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"text-primary-600 hover:text-primary-700 font-medium\",\n          children: \"Explore Topics \\u2192\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 13\n        }, this)]\n      }, subject.name, true, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-12 bg-primary-50 p-6 rounded-lg\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-xl font-semibold mb-2\",\n        children: \"Coming Soon\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-neutral-600\",\n        children: \"More subjects and detailed topic content are being added. Check back soon for updates!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = SubjectsPage;\nexport default SubjectsPage;\nvar _c;\n$RefreshReg$(_c, \"SubjectsPage\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "SubjectsPage", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "topics", "color", "map", "subject", "fill", "viewBox", "d", "_c", "$RefreshReg$"], "sources": ["D:/GrowthSch/IGCSEStuGuide/src/pages/SubjectsPage.tsx"], "sourcesContent": ["import React from 'react';\n\nconst SubjectsPage: React.FC = () => {\n  return (\n    <div>\n      <div className=\"mb-8\">\n        <h1>Subjects</h1>\n        <p className=\"text-neutral-600\">\n          Explore IGCSE subjects and their comprehensive study materials.\n        </p>\n      </div>\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {/* Placeholder subject cards */}\n        {[\n          { name: 'Mathematics', topics: 12, color: 'bg-blue-500' },\n          { name: 'Physics', topics: 10, color: 'bg-green-500' },\n          { name: 'Chemistry', topics: 11, color: 'bg-purple-500' },\n          { name: 'Biology', topics: 9, color: 'bg-red-500' },\n          { name: 'English Language', topics: 8, color: 'bg-yellow-500' },\n          { name: 'History', topics: 7, color: 'bg-indigo-500' },\n        ].map((subject) => (\n          <div key={subject.name} className=\"bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow\">\n            <div className={`w-12 h-12 ${subject.color} rounded-lg mb-4 flex items-center justify-center`}>\n              <svg className=\"w-6 h-6 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path d=\"M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z\"></path>\n              </svg>\n            </div>\n            <h3 className=\"text-lg font-semibold mb-2\">{subject.name}</h3>\n            <p className=\"text-neutral-600 mb-4\">{subject.topics} topics available</p>\n            <button className=\"text-primary-600 hover:text-primary-700 font-medium\">\n              Explore Topics →\n            </button>\n          </div>\n        ))}\n      </div>\n      \n      <div className=\"mt-12 bg-primary-50 p-6 rounded-lg\">\n        <h2 className=\"text-xl font-semibold mb-2\">Coming Soon</h2>\n        <p className=\"text-neutral-600\">\n          More subjects and detailed topic content are being added. Check back soon for updates!\n        </p>\n      </div>\n    </div>\n  );\n};\n\nexport default SubjectsPage;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EACnC,oBACED,OAAA;IAAAE,QAAA,gBACEF,OAAA;MAAKG,SAAS,EAAC,MAAM;MAAAD,QAAA,gBACnBF,OAAA;QAAAE,QAAA,EAAI;MAAQ;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjBP,OAAA;QAAGG,SAAS,EAAC,kBAAkB;QAAAD,QAAA,EAAC;MAEhC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAENP,OAAA;MAAKG,SAAS,EAAC,sDAAsD;MAAAD,QAAA,EAElE,CACC;QAAEM,IAAI,EAAE,aAAa;QAAEC,MAAM,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAc,CAAC,EACzD;QAAEF,IAAI,EAAE,SAAS;QAAEC,MAAM,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAe,CAAC,EACtD;QAAEF,IAAI,EAAE,WAAW;QAAEC,MAAM,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAgB,CAAC,EACzD;QAAEF,IAAI,EAAE,SAAS;QAAEC,MAAM,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAa,CAAC,EACnD;QAAEF,IAAI,EAAE,kBAAkB;QAAEC,MAAM,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAgB,CAAC,EAC/D;QAAEF,IAAI,EAAE,SAAS;QAAEC,MAAM,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAgB,CAAC,CACvD,CAACC,GAAG,CAAEC,OAAO,iBACZZ,OAAA;QAAwBG,SAAS,EAAC,qEAAqE;QAAAD,QAAA,gBACrGF,OAAA;UAAKG,SAAS,EAAE,aAAaS,OAAO,CAACF,KAAK,mDAAoD;UAAAR,QAAA,eAC5FF,OAAA;YAAKG,SAAS,EAAC,oBAAoB;YAACU,IAAI,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAZ,QAAA,eACzEF,OAAA;cAAMe,CAAC,EAAC;YAA4O;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNP,OAAA;UAAIG,SAAS,EAAC,4BAA4B;UAAAD,QAAA,EAAEU,OAAO,CAACJ;QAAI;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC9DP,OAAA;UAAGG,SAAS,EAAC,uBAAuB;UAAAD,QAAA,GAAEU,OAAO,CAACH,MAAM,EAAC,mBAAiB;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC1EP,OAAA;UAAQG,SAAS,EAAC,qDAAqD;UAAAD,QAAA,EAAC;QAExE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA,GAVDK,OAAO,CAACJ,IAAI;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAWjB,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENP,OAAA;MAAKG,SAAS,EAAC,oCAAoC;MAAAD,QAAA,gBACjDF,OAAA;QAAIG,SAAS,EAAC,4BAA4B;QAAAD,QAAA,EAAC;MAAW;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3DP,OAAA;QAAGG,SAAS,EAAC,kBAAkB;QAAAD,QAAA,EAAC;MAEhC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACS,EAAA,GA3CIf,YAAsB;AA6C5B,eAAeA,YAAY;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}