import React from 'react';

const FlashcardsPage: React.FC = () => {
  return (
    <div>
      <div className="mb-8">
        <h1>Flashcards</h1>
        <p className="text-neutral-600">
          Study with interactive flashcards using spaced repetition for better retention.
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Quick Stats</h2>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-neutral-600">Cards Reviewed Today:</span>
              <span className="font-semibold">0</span>
            </div>
            <div className="flex justify-between">
              <span className="text-neutral-600">Cards Due:</span>
              <span className="font-semibold text-primary-600">0</span>
            </div>
            <div className="flex justify-between">
              <span className="text-neutral-600">Total Cards:</span>
              <span className="font-semibold">0</span>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Study Progress</h2>
          <div className="space-y-3">
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm text-neutral-600">Mathematics</span>
                <span className="text-sm text-neutral-600">0%</span>
              </div>
              <div className="w-full bg-neutral-200 rounded-full h-2">
                <div className="bg-primary-600 h-2 rounded-full" style={{ width: '0%' }}></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm text-neutral-600">Physics</span>
                <span className="text-sm text-neutral-600">0%</span>
              </div>
              <div className="w-full bg-neutral-200 rounded-full h-2">
                <div className="bg-primary-600 h-2 rounded-full" style={{ width: '0%' }}></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="bg-white p-8 rounded-lg shadow-md text-center">
        <div className="mb-6">
          <svg className="w-16 h-16 text-neutral-400 mx-auto mb-4" fill="currentColor" viewBox="0 0 20 20">
            <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z"></path>
          </svg>
          <h2 className="text-xl font-semibold mb-2">No Flashcards Yet</h2>
          <p className="text-neutral-600 mb-6">
            Flashcard functionality is coming soon! You'll be able to create and study with interactive flashcards.
          </p>
        </div>
        
        <div className="space-y-4">
          <button className="bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out">
            Create Your First Flashcard Set
          </button>
          <p className="text-sm text-neutral-500">
            Features coming soon: Spaced repetition, flip animations, progress tracking
          </p>
        </div>
      </div>
    </div>
  );
};

export default FlashcardsPage;
