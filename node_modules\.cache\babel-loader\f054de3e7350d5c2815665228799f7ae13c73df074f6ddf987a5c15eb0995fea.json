{"ast": null, "code": "var _jsxFileName = \"D:\\\\GrowthSch\\\\IGCSEStuGuide\\\\src\\\\contexts\\\\AuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport { supabase } from '../lib/supabase';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext(undefined);\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [user, setUser] = useState(null);\n  const [session, setSession] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n  useEffect(() => {\n    // Get initial session\n    supabase.auth.getSession().then(({\n      data: {\n        session\n      }\n    }) => {\n      var _session$user;\n      setSession(session);\n      setUser((_session$user = session === null || session === void 0 ? void 0 : session.user) !== null && _session$user !== void 0 ? _session$user : null);\n      setIsLoading(false);\n    });\n\n    // Listen for auth changes\n    const {\n      data: {\n        subscription\n      }\n    } = supabase.auth.onAuthStateChange((_event, session) => {\n      var _session$user2;\n      setSession(session);\n      setUser((_session$user2 = session === null || session === void 0 ? void 0 : session.user) !== null && _session$user2 !== void 0 ? _session$user2 : null);\n      setIsLoading(false);\n    });\n    return () => subscription.unsubscribe();\n  }, []);\n  const signInWithMagicLink = async email => {\n    setIsLoading(true);\n    const {\n      error\n    } = await supabase.auth.signInWithOtp({\n      email,\n      options: {\n        emailRedirectTo: `${window.location.origin}/auth/callback`\n      }\n    });\n    setIsLoading(false);\n    return {\n      error\n    };\n  };\n  const signOut = async () => {\n    setIsLoading(true);\n    await supabase.auth.signOut();\n    setIsLoading(false);\n  };\n  const value = {\n    user,\n    session,\n    isLoading,\n    signInWithMagicLink,\n    signOut\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 66,\n    columnNumber: 10\n  }, this);\n};\n_s(AuthProvider, \"v3/zOfdXU0hsYF1ENx2NUyL1c2o=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "supabase", "jsxDEV", "_jsxDEV", "AuthContext", "undefined", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "user", "setUser", "session", "setSession", "isLoading", "setIsLoading", "auth", "getSession", "then", "data", "_session$user", "subscription", "onAuthStateChange", "_event", "_session$user2", "unsubscribe", "signInWithMagicLink", "email", "error", "signInWithOtp", "options", "emailRedirectTo", "window", "location", "origin", "signOut", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["D:/GrowthSch/IGCSEStuGuide/src/contexts/AuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useState } from 'react';\nimport { Session, User } from '@supabase/supabase-js';\nimport { supabase } from '../lib/supabase';\n\ninterface AuthContextType {\n  user: User | null;\n  session: Session | null;\n  isLoading: boolean;\n  signInWithMagicLink: (email: string) => Promise<{ error: any }>;\n  signOut: () => Promise<void>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [session, setSession] = useState<Session | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    // Get initial session\n    supabase.auth.getSession().then(({ data: { session } }) => {\n      setSession(session);\n      setUser(session?.user ?? null);\n      setIsLoading(false);\n    });\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      (_event, session) => {\n        setSession(session);\n        setUser(session?.user ?? null);\n        setIsLoading(false);\n      }\n    );\n\n    return () => subscription.unsubscribe();\n  }, []);\n\n  const signInWithMagicLink = async (email: string) => {\n    setIsLoading(true);\n    const { error } = await supabase.auth.signInWithOtp({\n      email,\n      options: {\n        emailRedirectTo: `${window.location.origin}/auth/callback`,\n      },\n    });\n    setIsLoading(false);\n    return { error };\n  };\n\n  const signOut = async () => {\n    setIsLoading(true);\n    await supabase.auth.signOut();\n    setIsLoading(false);\n  };\n\n  const value = {\n    user,\n    session,\n    isLoading,\n    signInWithMagicLink,\n    signOut,\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n};\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAE7E,SAASC,QAAQ,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAU3C,MAAMC,WAAW,gBAAGP,aAAa,CAA8BQ,SAAS,CAAC;AAEzE,OAAO,MAAMC,YAAqD,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACrF,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGV,QAAQ,CAAc,IAAI,CAAC;EACnD,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAiB,IAAI,CAAC;EAC5D,MAAM,CAACa,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAEhDD,SAAS,CAAC,MAAM;IACd;IACAE,QAAQ,CAACc,IAAI,CAACC,UAAU,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;MAAEC,IAAI,EAAE;QAAEP;MAAQ;IAAE,CAAC,KAAK;MAAA,IAAAQ,aAAA;MACzDP,UAAU,CAACD,OAAO,CAAC;MACnBD,OAAO,EAAAS,aAAA,GAACR,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEF,IAAI,cAAAU,aAAA,cAAAA,aAAA,GAAI,IAAI,CAAC;MAC9BL,YAAY,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC;;IAEF;IACA,MAAM;MAAEI,IAAI,EAAE;QAAEE;MAAa;IAAE,CAAC,GAAGnB,QAAQ,CAACc,IAAI,CAACM,iBAAiB,CAChE,CAACC,MAAM,EAAEX,OAAO,KAAK;MAAA,IAAAY,cAAA;MACnBX,UAAU,CAACD,OAAO,CAAC;MACnBD,OAAO,EAAAa,cAAA,GAACZ,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEF,IAAI,cAAAc,cAAA,cAAAA,cAAA,GAAI,IAAI,CAAC;MAC9BT,YAAY,CAAC,KAAK,CAAC;IACrB,CACF,CAAC;IAED,OAAO,MAAMM,YAAY,CAACI,WAAW,CAAC,CAAC;EACzC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,mBAAmB,GAAG,MAAOC,KAAa,IAAK;IACnDZ,YAAY,CAAC,IAAI,CAAC;IAClB,MAAM;MAAEa;IAAM,CAAC,GAAG,MAAM1B,QAAQ,CAACc,IAAI,CAACa,aAAa,CAAC;MAClDF,KAAK;MACLG,OAAO,EAAE;QACPC,eAAe,EAAE,GAAGC,MAAM,CAACC,QAAQ,CAACC,MAAM;MAC5C;IACF,CAAC,CAAC;IACFnB,YAAY,CAAC,KAAK,CAAC;IACnB,OAAO;MAAEa;IAAM,CAAC;EAClB,CAAC;EAED,MAAMO,OAAO,GAAG,MAAAA,CAAA,KAAY;IAC1BpB,YAAY,CAAC,IAAI,CAAC;IAClB,MAAMb,QAAQ,CAACc,IAAI,CAACmB,OAAO,CAAC,CAAC;IAC7BpB,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAMqB,KAAK,GAAG;IACZ1B,IAAI;IACJE,OAAO;IACPE,SAAS;IACTY,mBAAmB;IACnBS;EACF,CAAC;EAED,oBAAO/B,OAAA,CAACC,WAAW,CAACgC,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA5B,QAAA,EAAEA;EAAQ;IAAA8B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAuB,CAAC;AAC9E,CAAC;AAAChC,EAAA,CApDWF,YAAqD;AAAAmC,EAAA,GAArDnC,YAAqD;AAsDlE,OAAO,MAAMoC,OAAO,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC3B,MAAMC,OAAO,GAAG9C,UAAU,CAACM,WAAW,CAAC;EACvC,IAAIwC,OAAO,KAAKvC,SAAS,EAAE;IACzB,MAAM,IAAIwC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,OAAO;AAAA,IAAAD,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}