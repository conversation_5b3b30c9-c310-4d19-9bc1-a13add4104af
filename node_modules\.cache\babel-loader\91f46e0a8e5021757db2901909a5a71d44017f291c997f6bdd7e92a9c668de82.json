{"ast": null, "code": "var _jsxFileName = \"D:\\\\GrowthSch\\\\IGCSEStuGuide\\\\src\\\\pages\\\\AuthCallback.tsx\",\n  _s = $RefreshSig$();\nimport { useEffect, useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { supabase } from '../lib/supabase';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthCallback = () => {\n  _s();\n  const [error, setError] = useState(null);\n  const navigate = useNavigate();\n  useEffect(() => {\n    const handleAuthCallback = async () => {\n      const {\n        error\n      } = await supabase.auth.getSession();\n      if (error) {\n        setError(error.message);\n      } else {\n        // Redirect to dashboard or home page after successful authentication\n        navigate('/dashboard');\n      }\n    };\n    handleAuthCallback();\n  }, [navigate]);\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-md mx-auto mt-12 p-6 bg-white rounded-lg shadow-md\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-xl font-semibold text-red-600 mb-4\",\n        children: \"Authentication Error\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-neutral-700\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => navigate('/login'),\n        className: \"mt-4 px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700\",\n        children: \"Back to Login\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-md mx-auto mt-12 p-6 bg-white rounded-lg shadow-md text-center\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-xl font-semibold mb-4\",\n      children: \"Completing Sign In\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 40,\n    columnNumber: 5\n  }, this);\n};\n_s(AuthCallback, \"S97YeATJ+8xJ4yPil4tpqooM9lc=\", false, function () {\n  return [useNavigate];\n});\n_c = AuthCallback;\nexport default AuthCallback;\nvar _c;\n$RefreshReg$(_c, \"AuthCallback\");", "map": {"version": 3, "names": ["useEffect", "useState", "useNavigate", "supabase", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_s", "error", "setError", "navigate", "handleAuthCallback", "auth", "getSession", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/GrowthSch/IGCSEStuGuide/src/pages/AuthCallback.tsx"], "sourcesContent": ["import { useEffect, useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { supabase } from '../lib/supabase';\n\nconst AuthCallback = () => {\n  const [error, setError] = useState<string | null>(null);\n  const navigate = useNavigate();\n\n  useEffect(() => {\n    const handleAuthCallback = async () => {\n      const { error } = await supabase.auth.getSession();\n      \n      if (error) {\n        setError(error.message);\n      } else {\n        // Redirect to dashboard or home page after successful authentication\n        navigate('/dashboard');\n      }\n    };\n\n    handleAuthCallback();\n  }, [navigate]);\n\n  if (error) {\n    return (\n      <div className=\"max-w-md mx-auto mt-12 p-6 bg-white rounded-lg shadow-md\">\n        <h2 className=\"text-xl font-semibold text-red-600 mb-4\">Authentication Error</h2>\n        <p className=\"text-neutral-700\">{error}</p>\n        <button\n          onClick={() => navigate('/login')}\n          className=\"mt-4 px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700\"\n        >\n          Back to Login\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-md mx-auto mt-12 p-6 bg-white rounded-lg shadow-md text-center\">\n      <h2 className=\"text-xl font-semibold mb-4\">Completing Sign In</h2>\n      <div className=\"flex justify-center\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600\"></div>\n      </div>\n    </div>\n  );\n};\n\nexport default AuthCallback;"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,QAAQ,QAAQ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGR,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAMS,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAE9BF,SAAS,CAAC,MAAM;IACd,MAAMW,kBAAkB,GAAG,MAAAA,CAAA,KAAY;MACrC,MAAM;QAAEH;MAAM,CAAC,GAAG,MAAML,QAAQ,CAACS,IAAI,CAACC,UAAU,CAAC,CAAC;MAElD,IAAIL,KAAK,EAAE;QACTC,QAAQ,CAACD,KAAK,CAACM,OAAO,CAAC;MACzB,CAAC,MAAM;QACL;QACAJ,QAAQ,CAAC,YAAY,CAAC;MACxB;IACF,CAAC;IAEDC,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACD,QAAQ,CAAC,CAAC;EAEd,IAAIF,KAAK,EAAE;IACT,oBACEH,OAAA;MAAKU,SAAS,EAAC,0DAA0D;MAAAC,QAAA,gBACvEX,OAAA;QAAIU,SAAS,EAAC,yCAAyC;QAAAC,QAAA,EAAC;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjFf,OAAA;QAAGU,SAAS,EAAC,kBAAkB;QAAAC,QAAA,EAAER;MAAK;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3Cf,OAAA;QACEgB,OAAO,EAAEA,CAAA,KAAMX,QAAQ,CAAC,QAAQ,CAAE;QAClCK,SAAS,EAAC,0EAA0E;QAAAC,QAAA,EACrF;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACEf,OAAA;IAAKU,SAAS,EAAC,sEAAsE;IAAAC,QAAA,gBACnFX,OAAA;MAAIU,SAAS,EAAC,4BAA4B;MAAAC,QAAA,EAAC;IAAkB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAClEf,OAAA;MAAKU,SAAS,EAAC,qBAAqB;MAAAC,QAAA,eAClCX,OAAA;QAAKU,SAAS,EAAC;MAA8E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACb,EAAA,CA1CID,YAAY;EAAA,QAECJ,WAAW;AAAA;AAAAoB,EAAA,GAFxBhB,YAAY;AA4ClB,eAAeA,YAAY;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}