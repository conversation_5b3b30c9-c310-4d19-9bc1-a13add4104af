# IGCSE Grade 9-10 Student Guide Web Application

## Project Overview
A modern, responsive web application targeted at IGCSE Grade 9-10 students to improve learning, engagement, and retention through interactive study tools.

## Tech Stack
- **Frontend**: React with Tailwind CSS
- **Backend**: Supabase (Authentication, PostgreSQL Database, Storage)
- **Deployment**: Vercel or Netlify

## UI Design System

### Color Palette
- **Primary**: Educational blue (#4338ca, #6366f1) - Trust, knowledge, focus
- **Secondary**: Teal accent (#0f766e, #14b8a6) - Growth, achievement
- **Neutral**: Slate grayscale (#f8fafc to #0f172a) - Content hierarchy
- **Accent**: Strategic highlights for notifications, achievements

### Typography
- **Primary Font**: Inter (sans-serif) for body text and UI
- **Display Font**: Lexend for headings and emphasis
- **Scale**: 
  - Headings: 2.25rem to 1.25rem
  - Body: 1rem (16px)
  - Small text: 0.875rem

### Component Hierarchy
1. **Layout Components**
   - AppShell (main layout wrapper)
   - Navbar/Sidebar
   - ContentArea
   - Footer

2. **Authentication Components**
   - LoginForm
   - MagicLinkRequest
   - VerificationPrompt
   - UserProfileCard

3. **Study Content Components**
   - SubjectCard
   - TopicList
   - ContentViewer
   - FlashcardComponent
   - QuizInterface

4. **Dashboard Components**
   - ProgressChart
   - StudyStreakTracker
   - RecentActivityFeed
   - PerformanceMetrics

## MVP Features

### 1. User Authentication
- Email-based authentication with magic link sign-in (passwordless)
- Email verification flow using Supabase Auth
- Protected routes for authenticated users
- User profile management with session handling
- Secure authentication state persistence:
  - Implement token refresh using Supabase Auth middleware
  - Store session data in HTTP-only cookies for SSR security
  - Add auth state change listeners for real-time session updates
  - Implement proper session expiry and auto-logout mechanisms

### 2. Flashcard System
- Subject and topic-based organization in PostgreSQL tables
- Interactive flip animation
- Spaced repetition algorithm (basic implementation)
- Save/favorite functionality with real-time updates

### 3. Subject Content
- Structured subject summaries with rich formatting
- Topic navigation and search using Postgres full-text search
- Markdown/rich text support for content
- Visual aids and diagrams stored in Supabase Storage

### 4. Quiz Module
- Multiple-choice questions by subject/topic
- Immediate feedback on answers
- Score tracking and review with Postgres for analytics
- Difficulty levels

### 5. Progress Dashboard
- Visual progress indicators
- Performance analytics using Supabase database queries
- Study streak tracking
- Topic completion status

## Implementation Phases

### Phase 0: Design & Planning (Week 0)
- Create wireframes and mockups for key screens
- Establish design system tokens and guidelines
- Set up project repository and development environment
- Configure Tailwind CSS with custom theme

### Phase 1: Setup & Authentication (Week 1)
- Project scaffolding with Create React App or Next.js
- Supabase project setup and configuration
- UI Implementation:
  - Create layout components (AppShell, Navbar)
  - Build authentication UI components
  - Implement responsive design foundation
- Authentication implementation:
  - Configure Supabase Auth settings for email verification
  - Implement `signInWithOtp` for magic link authentication
  - Create auth context provider for session management
  - Set up email templates for verification and magic links
  - Implement auth state change listeners
- Basic routing and protected routes with auth redirects
- User profile database schema design

### Phase 2: Core Content (Weeks 2-3)
- Subject/topic data structure in PostgreSQL
- UI Implementation:
  - Build subject and topic browsing components
  - Create content viewer with Markdown support
  - Develop flashcard component with flip animation
- Flashcard component development
- Subject summary components
- Content management system with Supabase Storage
- UI Review and refinement

### Phase 3: Interactive Features (Weeks 4-5)
- UI Implementation:
  - Build quiz interface components
  - Create dashboard widgets and charts
  - Implement progress visualization components
- Quiz system implementation with Postgres for data storage
- Progress tracking logic with database functions
- User dashboard with real-time updates
- Performance optimization
- Responsive design testing and fixes

### Phase 4: Testing & Deployment (Week 6)
- User testing and feedback
- UI polish and refinement
- Bug fixes and refinements
- Deployment to Vercel/Netlify with Supabase backend
- Documentation
- Final UI/UX review

## UI Development Milestones

1. **Week 0**: Design system established, wireframes completed
2. **Week 1**: Layout and authentication components built
3. **Week 2**: Subject browsing and content viewing components completed
4. **Week 3**: Flashcard interface implemented
5. **Week 4**: Quiz interface components built
6. **Week 5**: Dashboard and progress visualization components completed
7. **Week 6**: Final UI polish and responsive design fixes

## Future Enhancements
- Adaptive learning algorithm
- Advanced spaced repetition
- Collaborative study groups using Supabase real-time subscriptions
- Mobile app version
- Offline functionality with local storage sync

## Recommended Libraries
- **@supabase/supabase-js** for core Supabase integration
- **@supabase/auth-helpers-react** for React auth hooks and utilities
- **@supabase/auth-ui-react** for pre-built auth UI components
- **react-markdown** for rendering markdown content
- **react-card-flip** for flashcard animations
- **recharts** for dashboard visualizations
- **framer-motion** for smooth UI animations
- **@tailwindcss/typography** for rich text styling
- **@tailwindcss/forms** for form element styling

## Authentication Best Practices
- Use Supabase's built-in email templates with custom branding
- Implement proper session refresh mechanisms
- Store user session in secure cookies for SSR applications
- Add rate limiting for auth attempts
- Create a fallback UI for authentication errors
- Implement proper loading states during authentication processes

## Tailwind CSS Configuration

```js
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eef2ff',
          100: '#e0e7ff',
          500: '#6366f1',
          600: '#4f46e5',
          700: '#4338ca',
        },
        secondary: {
          50: '#f0fdfa',
          100: '#ccfbf1',
          500: '#14b8a6',
          600: '#0d9488',
          700: '#0f766e',
        },
        neutral: {
          50: '#f8fafc',
          100: '#f1f5f9',
          200: '#e2e8f0',
          700: '#334155',
          800: '#1e293b',
          900: '#0f172a',
        }
      },
      fontFamily: {
        sans: ['Inter', 'sans-serif'],
        display: ['Lexend', 'sans-serif'],
      },
      spacing: {
        '128': '32rem',
        '144': '36rem',
      },
      borderRadius: {
        '4xl': '2rem',
      }
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
    require('@tailwindcss/forms'),
  ],
}
- Implement proper loading states during authentication processes
