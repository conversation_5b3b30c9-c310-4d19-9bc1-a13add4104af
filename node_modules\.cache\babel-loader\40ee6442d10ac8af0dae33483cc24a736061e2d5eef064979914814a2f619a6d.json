{"ast": null, "code": "var _jsxFileName = \"D:\\\\GrowthSch\\\\IGCSEStuGuide\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { AuthProvider, useAuth } from './contexts/AuthContext';\nimport AppShell from './components/layout/AppShell';\nimport LoginPage from './pages/LoginPage';\nimport DashboardPage from './pages/DashboardPage';\nimport HomePage from './pages/HomePage';\nimport AuthCallback from './pages/AuthCallback';\nimport SubjectsPage from './pages/SubjectsPage';\nimport FlashcardsPage from './pages/FlashcardsPage';\nimport QuizzesPage from './pages/QuizzesPage';\n\n// Protected route wrapper component\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children\n}) => {\n  _s();\n  const {\n    user,\n    isLoading\n  } = useAuth();\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-center items-center h-screen\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this);\n  }\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: children\n  }, void 0, false);\n};\n_s(ProtectedRoute, \"6lKHjqCqGIRsHh92bje8H78laow=\", false, function () {\n  return [useAuth];\n});\n_c = ProtectedRoute;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(AuthProvider, {\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(AppShell, {\n            children: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/login\",\n          element: /*#__PURE__*/_jsxDEV(AppShell, {\n            children: /*#__PURE__*/_jsxDEV(LoginPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 51\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/auth/callback\",\n          element: /*#__PURE__*/_jsxDEV(AuthCallback, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 49\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/dashboard\",\n          element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n            children: /*#__PURE__*/_jsxDEV(AppShell, {\n              children: /*#__PURE__*/_jsxDEV(DashboardPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 45,\n                columnNumber: 27\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/subjects\",\n          element: /*#__PURE__*/_jsxDEV(AppShell, {\n            children: /*#__PURE__*/_jsxDEV(SubjectsPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 25\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/flashcards\",\n          element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n            children: /*#__PURE__*/_jsxDEV(AppShell, {\n              children: /*#__PURE__*/_jsxDEV(FlashcardsPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 59,\n                columnNumber: 27\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/quizzes\",\n          element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n            children: /*#__PURE__*/_jsxDEV(AppShell, {\n              children: /*#__PURE__*/_jsxDEV(QuizzesPage, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 67,\n                columnNumber: 27\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 5\n  }, this);\n}\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"ProtectedRoute\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "<PERSON>th<PERSON><PERSON><PERSON>", "useAuth", "AppShell", "LoginPage", "DashboardPage", "HomePage", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SubjectsPage", "FlashcardsPage", "QuizzesPage", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProtectedRoute", "children", "_s", "user", "isLoading", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "replace", "_c", "App", "path", "element", "_c2", "$RefreshReg$"], "sources": ["D:/GrowthSch/IGCSEStuGuide/src/App.tsx"], "sourcesContent": ["import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { AuthProvider, useAuth } from './contexts/AuthContext';\nimport AppShell from './components/layout/AppShell';\nimport LoginPage from './pages/LoginPage';\nimport DashboardPage from './pages/DashboardPage';\nimport HomePage from './pages/HomePage';\nimport AuthCallback from './pages/AuthCallback';\nimport SubjectsPage from './pages/SubjectsPage';\nimport FlashcardsPage from './pages/FlashcardsPage';\nimport QuizzesPage from './pages/QuizzesPage';\n\n// Protected route wrapper component\nconst ProtectedRoute = ({ children }: { children: React.ReactNode }) => {\n  const { user, isLoading } = useAuth();\n  \n  if (isLoading) {\n    return (\n      <div className=\"flex justify-center items-center h-screen\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600\"></div>\n      </div>\n    );\n  }\n  \n  if (!user) {\n    return <Navigate to=\"/login\" replace />;\n  }\n  \n  return <>{children}</>;\n};\n\nfunction App() {\n  return (\n    <Router>\n      <AuthProvider>\n        <Routes>\n          <Route path=\"/\" element={<AppShell><HomePage /></AppShell>} />\n          <Route path=\"/login\" element={<AppShell><LoginPage /></AppShell>} />\n          <Route path=\"/auth/callback\" element={<AuthCallback />} />\n          \n          {/* Protected routes */}\n          <Route \n            path=\"/dashboard\" \n            element={\n              <ProtectedRoute>\n                <AppShell><DashboardPage /></AppShell>\n              </ProtectedRoute>\n            } \n          />\n          <Route \n            path=\"/subjects\" \n            element={\n              <AppShell><SubjectsPage /></AppShell>\n            } \n          />\n          <Route \n            path=\"/flashcards\" \n            element={\n              <ProtectedRoute>\n                <AppShell><FlashcardsPage /></AppShell>\n              </ProtectedRoute>\n            } \n          />\n          <Route \n            path=\"/quizzes\" \n            element={\n              <ProtectedRoute>\n                <AppShell><QuizzesPage /></AppShell>\n              </ProtectedRoute>\n            } \n          />\n        </Routes>\n      </AuthProvider>\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,SAASA,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,YAAY,EAAEC,OAAO,QAAQ,wBAAwB;AAC9D,OAAOC,QAAQ,MAAM,8BAA8B;AACnD,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,WAAW,MAAM,qBAAqB;;AAE7C;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,cAAc,GAAGA,CAAC;EAAEC;AAAwC,CAAC,KAAK;EAAAC,EAAA;EACtE,MAAM;IAAEC,IAAI;IAAEC;EAAU,CAAC,GAAGjB,OAAO,CAAC,CAAC;EAErC,IAAIiB,SAAS,EAAE;IACb,oBACEP,OAAA;MAAKQ,SAAS,EAAC,2CAA2C;MAAAJ,QAAA,eACxDJ,OAAA;QAAKQ,SAAS,EAAC;MAA8E;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjG,CAAC;EAEV;EAEA,IAAI,CAACN,IAAI,EAAE;IACT,oBAAON,OAAA,CAACZ,QAAQ;MAACyB,EAAE,EAAC,QAAQ;MAACC,OAAO;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACzC;EAEA,oBAAOZ,OAAA,CAAAE,SAAA;IAAAE,QAAA,EAAGA;EAAQ,gBAAG,CAAC;AACxB,CAAC;AAACC,EAAA,CAhBIF,cAAc;EAAA,QACUb,OAAO;AAAA;AAAAyB,EAAA,GAD/BZ,cAAc;AAkBpB,SAASa,GAAGA,CAAA,EAAG;EACb,oBACEhB,OAAA,CAACf,MAAM;IAAAmB,QAAA,eACLJ,OAAA,CAACX,YAAY;MAAAe,QAAA,eACXJ,OAAA,CAACd,MAAM;QAAAkB,QAAA,gBACLJ,OAAA,CAACb,KAAK;UAAC8B,IAAI,EAAC,GAAG;UAACC,OAAO,eAAElB,OAAA,CAACT,QAAQ;YAAAa,QAAA,eAACJ,OAAA,CAACN,QAAQ;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9DZ,OAAA,CAACb,KAAK;UAAC8B,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAElB,OAAA,CAACT,QAAQ;YAAAa,QAAA,eAACJ,OAAA,CAACR,SAAS;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpEZ,OAAA,CAACb,KAAK;UAAC8B,IAAI,EAAC,gBAAgB;UAACC,OAAO,eAAElB,OAAA,CAACL,YAAY;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG1DZ,OAAA,CAACb,KAAK;UACJ8B,IAAI,EAAC,YAAY;UACjBC,OAAO,eACLlB,OAAA,CAACG,cAAc;YAAAC,QAAA,eACbJ,OAAA,CAACT,QAAQ;cAAAa,QAAA,eAACJ,OAAA,CAACP,aAAa;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB;QACjB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACFZ,OAAA,CAACb,KAAK;UACJ8B,IAAI,EAAC,WAAW;UAChBC,OAAO,eACLlB,OAAA,CAACT,QAAQ;YAAAa,QAAA,eAACJ,OAAA,CAACJ,YAAY;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU;QACrC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACFZ,OAAA,CAACb,KAAK;UACJ8B,IAAI,EAAC,aAAa;UAClBC,OAAO,eACLlB,OAAA,CAACG,cAAc;YAAAC,QAAA,eACbJ,OAAA,CAACT,QAAQ;cAAAa,QAAA,eAACJ,OAAA,CAACH,cAAc;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB;QACjB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACFZ,OAAA,CAACb,KAAK;UACJ8B,IAAI,EAAC,UAAU;UACfC,OAAO,eACLlB,OAAA,CAACG,cAAc;YAAAC,QAAA,eACbJ,OAAA,CAACT,QAAQ;cAAAa,QAAA,eAACJ,OAAA,CAACF,WAAW;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QACjB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEb;AAACO,GAAA,GA5CQH,GAAG;AA8CZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAI,GAAA;AAAAC,YAAA,CAAAL,EAAA;AAAAK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}