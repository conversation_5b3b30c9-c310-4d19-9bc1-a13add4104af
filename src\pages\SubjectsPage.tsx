import React from 'react';

const SubjectsPage: React.FC = () => {
  return (
    <div>
      <div className="mb-8">
        <h1>Subjects</h1>
        <p className="text-neutral-600">
          Explore IGCSE subjects and their comprehensive study materials.
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Placeholder subject cards */}
        {[
          { name: 'Mathematics', topics: 12, color: 'bg-blue-500' },
          { name: 'Physics', topics: 10, color: 'bg-green-500' },
          { name: 'Chemistry', topics: 11, color: 'bg-purple-500' },
          { name: 'Biology', topics: 9, color: 'bg-red-500' },
          { name: 'English Language', topics: 8, color: 'bg-yellow-500' },
          { name: 'History', topics: 7, color: 'bg-indigo-500' },
        ].map((subject) => (
          <div key={subject.name} className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow">
            <div className={`w-12 h-12 ${subject.color} rounded-lg mb-4 flex items-center justify-center`}>
              <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z"></path>
              </svg>
            </div>
            <h3 className="text-lg font-semibold mb-2">{subject.name}</h3>
            <p className="text-neutral-600 mb-4">{subject.topics} topics available</p>
            <button className="text-primary-600 hover:text-primary-700 font-medium">
              Explore Topics →
            </button>
          </div>
        ))}
      </div>
      
      <div className="mt-12 bg-primary-50 p-6 rounded-lg">
        <h2 className="text-xl font-semibold mb-2">Coming Soon</h2>
        <p className="text-neutral-600">
          More subjects and detailed topic content are being added. Check back soon for updates!
        </p>
      </div>
    </div>
  );
};

export default SubjectsPage;
