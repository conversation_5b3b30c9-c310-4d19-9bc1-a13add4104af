{"ast": null, "code": "var _jsxFileName = \"D:\\\\GrowthSch\\\\IGCSEStuGuide\\\\src\\\\pages\\\\LoginPage.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport LoginForm from '../components/auth/LoginForm';\nimport { Navigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoginPage = () => {\n  _s();\n  const {\n    user,\n    isLoading\n  } = useAuth();\n\n  // Redirect if already logged in\n  if (user && !isLoading) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/dashboard\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-4xl mx-auto py-12\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"text-center mb-8\",\n      children: \"Welcome to IGCSE Student Guide\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-lg p-6 md:p-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"md:flex\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"md:w-1/2 md:pr-8 mb-6 md:mb-0\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold mb-4\",\n            children: \"Your Learning Journey Starts Here\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-neutral-600 mb-4\",\n            children: \"Access comprehensive study materials, interactive flashcards, and quizzes designed specifically for IGCSE Grade 9-10 students.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"space-y-2 text-neutral-600\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-5 h-5 text-secondary-500 mr-2\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 27,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 26,\n                columnNumber: 17\n              }, this), \"Structured subject content\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-5 h-5 text-secondary-500 mr-2\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 33,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 32,\n                columnNumber: 17\n              }, this), \"Interactive flashcards\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-5 h-5 text-secondary-500 mr-2\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 39,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 38,\n                columnNumber: 17\n              }, this), \"Practice quizzes\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-5 h-5 text-secondary-500 mr-2\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 45,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 44,\n                columnNumber: 17\n              }, this), \"Progress tracking\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"md:w-1/2\",\n          children: /*#__PURE__*/_jsxDEV(LoginForm, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n};\n_s(LoginPage, \"6lKHjqCqGIRsHh92bje8H78laow=\", false, function () {\n  return [useAuth];\n});\n_c = LoginPage;\nexport default LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");", "map": {"version": 3, "names": ["React", "LoginForm", "Navigate", "useAuth", "jsxDEV", "_jsxDEV", "LoginPage", "_s", "user", "isLoading", "to", "replace", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "fill", "viewBox", "fillRule", "d", "clipRule", "_c", "$RefreshReg$"], "sources": ["D:/GrowthSch/IGCSEStuGuide/src/pages/LoginPage.tsx"], "sourcesContent": ["import React from 'react';\nimport LoginForm from '../components/auth/LoginForm';\nimport { Navigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst LoginPage: React.FC = () => {\n  const { user, isLoading } = useAuth();\n  \n  // Redirect if already logged in\n  if (user && !isLoading) {\n    return <Navigate to=\"/dashboard\" replace />;\n  }\n  \n  return (\n    <div className=\"max-w-4xl mx-auto py-12\">\n      <h1 className=\"text-center mb-8\">Welcome to IGCSE Student Guide</h1>\n      <div className=\"bg-white rounded-lg shadow-lg p-6 md:p-8\">\n        <div className=\"md:flex\">\n          <div className=\"md:w-1/2 md:pr-8 mb-6 md:mb-0\">\n            <h2 className=\"text-xl font-semibold mb-4\">Your Learning Journey Starts Here</h2>\n            <p className=\"text-neutral-600 mb-4\">\n              Access comprehensive study materials, interactive flashcards, and quizzes designed specifically for IGCSE Grade 9-10 students.\n            </p>\n            <ul className=\"space-y-2 text-neutral-600\">\n              <li className=\"flex items-center\">\n                <svg className=\"w-5 h-5 text-secondary-500 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                </svg>\n                Structured subject content\n              </li>\n              <li className=\"flex items-center\">\n                <svg className=\"w-5 h-5 text-secondary-500 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                </svg>\n                Interactive flashcards\n              </li>\n              <li className=\"flex items-center\">\n                <svg className=\"w-5 h-5 text-secondary-500 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                </svg>\n                Practice quizzes\n              </li>\n              <li className=\"flex items-center\">\n                <svg className=\"w-5 h-5 text-secondary-500 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                </svg>\n                Progress tracking\n              </li>\n            </ul>\n          </div>\n          <div className=\"md:w-1/2\">\n            <LoginForm />\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default LoginPage;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,8BAA8B;AACpD,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM;IAAEC,IAAI;IAAEC;EAAU,CAAC,GAAGN,OAAO,CAAC,CAAC;;EAErC;EACA,IAAIK,IAAI,IAAI,CAACC,SAAS,EAAE;IACtB,oBAAOJ,OAAA,CAACH,QAAQ;MAACQ,EAAE,EAAC,YAAY;MAACC,OAAO;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC7C;EAEA,oBACEV,OAAA;IAAKW,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtCZ,OAAA;MAAIW,SAAS,EAAC,kBAAkB;MAAAC,QAAA,EAAC;IAA8B;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACpEV,OAAA;MAAKW,SAAS,EAAC,0CAA0C;MAAAC,QAAA,eACvDZ,OAAA;QAAKW,SAAS,EAAC,SAAS;QAAAC,QAAA,gBACtBZ,OAAA;UAAKW,SAAS,EAAC,+BAA+B;UAAAC,QAAA,gBAC5CZ,OAAA;YAAIW,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAAiC;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjFV,OAAA;YAAGW,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAErC;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJV,OAAA;YAAIW,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACxCZ,OAAA;cAAIW,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC/BZ,OAAA;gBAAKW,SAAS,EAAC,iCAAiC;gBAACE,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAF,QAAA,eACtFZ,OAAA;kBAAMe,QAAQ,EAAC,SAAS;kBAACC,CAAC,EAAC,uIAAuI;kBAACC,QAAQ,EAAC;gBAAS;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrL,CAAC,8BAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLV,OAAA;cAAIW,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC/BZ,OAAA;gBAAKW,SAAS,EAAC,iCAAiC;gBAACE,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAF,QAAA,eACtFZ,OAAA;kBAAMe,QAAQ,EAAC,SAAS;kBAACC,CAAC,EAAC,uIAAuI;kBAACC,QAAQ,EAAC;gBAAS;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrL,CAAC,0BAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLV,OAAA;cAAIW,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC/BZ,OAAA;gBAAKW,SAAS,EAAC,iCAAiC;gBAACE,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAF,QAAA,eACtFZ,OAAA;kBAAMe,QAAQ,EAAC,SAAS;kBAACC,CAAC,EAAC,uIAAuI;kBAACC,QAAQ,EAAC;gBAAS;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrL,CAAC,oBAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLV,OAAA;cAAIW,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC/BZ,OAAA;gBAAKW,SAAS,EAAC,iCAAiC;gBAACE,IAAI,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAF,QAAA,eACtFZ,OAAA;kBAAMe,QAAQ,EAAC,SAAS;kBAACC,CAAC,EAAC,uIAAuI;kBAACC,QAAQ,EAAC;gBAAS;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrL,CAAC,qBAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACNV,OAAA;UAAKW,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBZ,OAAA,CAACJ,SAAS;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACR,EAAA,CApDID,SAAmB;EAAA,QACKH,OAAO;AAAA;AAAAoB,EAAA,GAD/BjB,SAAmB;AAsDzB,eAAeA,SAAS;AAAC,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}