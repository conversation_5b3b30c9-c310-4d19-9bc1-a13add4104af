{"ast": null, "code": "var _jsxFileName = \"D:\\\\GrowthSch\\\\IGCSEStuGuide\\\\src\\\\components\\\\layout\\\\Navbar.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Navbar = () => {\n  _s();\n  const {\n    user,\n    signOut\n  } = useAuth();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"bg-primary-600 text-white shadow-md\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center py-4\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"font-display text-xl font-bold\",\n          children: \"IGCSE Guide\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"md:hidden\",\n          onClick: () => setIsMenuOpen(!isMenuOpen),\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-6 h-6\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: isMenuOpen ? /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M6 18L18 6M6 6l12 12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M4 6h16M4 12h16M4 18h16\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden md:flex items-center space-x-6\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/subjects\",\n            className: \"hover:text-primary-100\",\n            children: \"Subjects\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/flashcards\",\n            className: \"hover:text-primary-100\",\n            children: \"Flashcards\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/quizzes\",\n            className: \"hover:text-primary-100\",\n            children: \"Quizzes\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this), user ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/dashboard\",\n              className: \"hover:text-primary-100\",\n              children: \"Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 38,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: signOut,\n              className: \"bg-primary-700 hover:bg-primary-800 px-4 py-2 rounded-md\",\n              children: \"Sign Out\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/login\",\n            className: \"bg-secondary-500 hover:bg-secondary-600 px-4 py-2 rounded-md\",\n            children: \"Sign In\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this), isMenuOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"md:hidden py-4 space-y-3\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/subjects\",\n          className: \"block hover:text-primary-100\",\n          children: \"Subjects\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/flashcards\",\n          className: \"block hover:text-primary-100\",\n          children: \"Flashcards\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/quizzes\",\n          className: \"block hover:text-primary-100\",\n          children: \"Quizzes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 13\n        }, this), user ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/dashboard\",\n            className: \"block hover:text-primary-100\",\n            children: \"Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: signOut,\n            className: \"block w-full text-left bg-primary-700 hover:bg-primary-800 px-4 py-2 rounded-md\",\n            children: \"Sign Out\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/login\",\n          className: \"block w-full text-center bg-secondary-500 hover:bg-secondary-600 px-4 py-2 rounded-md\",\n          children: \"Sign In\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n};\n_s(Navbar, \"mQrkE3PGjqyBo9viockcJAWqUnU=\", false, function () {\n  return [useAuth];\n});\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON>", "_s", "user", "signOut", "isMenuOpen", "setIsMenuOpen", "className", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["D:/GrowthSch/IGCSEStuGuide/src/components/layout/Navbar.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\n\nconst Navbar: React.FC = () => {\n  const { user, signOut } = useAuth();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  return (\n    <nav className=\"bg-primary-600 text-white shadow-md\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex justify-between items-center py-4\">\n          <Link to=\"/\" className=\"font-display text-xl font-bold\">\n            IGCSE Guide\n          </Link>\n          \n          {/* Mobile menu button */}\n          <button \n            className=\"md:hidden\"\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n          >\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              {isMenuOpen ? (\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              ) : (\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n              )}\n            </svg>\n          </button>\n          \n          {/* Desktop menu */}\n          <div className=\"hidden md:flex items-center space-x-6\">\n            <Link to=\"/subjects\" className=\"hover:text-primary-100\">Subjects</Link>\n            <Link to=\"/flashcards\" className=\"hover:text-primary-100\">Flashcards</Link>\n            <Link to=\"/quizzes\" className=\"hover:text-primary-100\">Quizzes</Link>\n            {user ? (\n              <>\n                <Link to=\"/dashboard\" className=\"hover:text-primary-100\">Dashboard</Link>\n                <button \n                  onClick={signOut}\n                  className=\"bg-primary-700 hover:bg-primary-800 px-4 py-2 rounded-md\"\n                >\n                  Sign Out\n                </button>\n              </>\n            ) : (\n              <Link \n                to=\"/login\" \n                className=\"bg-secondary-500 hover:bg-secondary-600 px-4 py-2 rounded-md\"\n              >\n                Sign In\n              </Link>\n            )}\n          </div>\n        </div>\n        \n        {/* Mobile menu */}\n        {isMenuOpen && (\n          <div className=\"md:hidden py-4 space-y-3\">\n            <Link to=\"/subjects\" className=\"block hover:text-primary-100\">Subjects</Link>\n            <Link to=\"/flashcards\" className=\"block hover:text-primary-100\">Flashcards</Link>\n            <Link to=\"/quizzes\" className=\"block hover:text-primary-100\">Quizzes</Link>\n            {user ? (\n              <>\n                <Link to=\"/dashboard\" className=\"block hover:text-primary-100\">Dashboard</Link>\n                <button \n                  onClick={signOut}\n                  className=\"block w-full text-left bg-primary-700 hover:bg-primary-800 px-4 py-2 rounded-md\"\n                >\n                  Sign Out\n                </button>\n              </>\n            ) : (\n              <Link \n                to=\"/login\" \n                className=\"block w-full text-center bg-secondary-500 hover:bg-secondary-600 px-4 py-2 rounded-md\"\n              >\n                Sign In\n              </Link>\n            )}\n          </div>\n        )}\n      </div>\n    </nav>\n  );\n};\n\nexport default Navbar;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,OAAO,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErD,MAAMC,MAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAGR,OAAO,CAAC,CAAC;EACnC,MAAM,CAACS,UAAU,EAAEC,aAAa,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAEnD,oBACEI,OAAA;IAAKS,SAAS,EAAC,qCAAqC;IAAAC,QAAA,eAClDV,OAAA;MAAKS,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACrCV,OAAA;QAAKS,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDV,OAAA,CAACH,IAAI;UAACc,EAAE,EAAC,GAAG;UAACF,SAAS,EAAC,gCAAgC;UAAAC,QAAA,EAAC;QAExD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAGPf,OAAA;UACES,SAAS,EAAC,WAAW;UACrBO,OAAO,EAAEA,CAAA,KAAMR,aAAa,CAAC,CAACD,UAAU,CAAE;UAAAG,QAAA,eAE1CV,OAAA;YAAKS,SAAS,EAAC,SAAS;YAACQ,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAT,QAAA,EAC3EH,UAAU,gBACTP,OAAA;cAAMoB,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAsB;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAE9Ff,OAAA;cAAMoB,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAyB;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UACjG;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAGTf,OAAA;UAAKS,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDV,OAAA,CAACH,IAAI;YAACc,EAAE,EAAC,WAAW;YAACF,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvEf,OAAA,CAACH,IAAI;YAACc,EAAE,EAAC,aAAa;YAACF,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3Ef,OAAA,CAACH,IAAI;YAACc,EAAE,EAAC,UAAU;YAACF,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EACpEV,IAAI,gBACHL,OAAA,CAAAE,SAAA;YAAAQ,QAAA,gBACEV,OAAA,CAACH,IAAI;cAACc,EAAE,EAAC,YAAY;cAACF,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzEf,OAAA;cACEgB,OAAO,EAAEV,OAAQ;cACjBG,SAAS,EAAC,0DAA0D;cAAAC,QAAA,EACrE;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,eACT,CAAC,gBAEHf,OAAA,CAACH,IAAI;YACHc,EAAE,EAAC,QAAQ;YACXF,SAAS,EAAC,8DAA8D;YAAAC,QAAA,EACzE;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLR,UAAU,iBACTP,OAAA;QAAKS,SAAS,EAAC,0BAA0B;QAAAC,QAAA,gBACvCV,OAAA,CAACH,IAAI;UAACc,EAAE,EAAC,WAAW;UAACF,SAAS,EAAC,8BAA8B;UAAAC,QAAA,EAAC;QAAQ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC7Ef,OAAA,CAACH,IAAI;UAACc,EAAE,EAAC,aAAa;UAACF,SAAS,EAAC,8BAA8B;UAAAC,QAAA,EAAC;QAAU;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjFf,OAAA,CAACH,IAAI;UAACc,EAAE,EAAC,UAAU;UAACF,SAAS,EAAC,8BAA8B;UAAAC,QAAA,EAAC;QAAO;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAC1EV,IAAI,gBACHL,OAAA,CAAAE,SAAA;UAAAQ,QAAA,gBACEV,OAAA,CAACH,IAAI;YAACc,EAAE,EAAC,YAAY;YAACF,SAAS,EAAC,8BAA8B;YAAAC,QAAA,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/Ef,OAAA;YACEgB,OAAO,EAAEV,OAAQ;YACjBG,SAAS,EAAC,iFAAiF;YAAAC,QAAA,EAC5F;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACT,CAAC,gBAEHf,OAAA,CAACH,IAAI;UACHc,EAAE,EAAC,QAAQ;UACXF,SAAS,EAAC,uFAAuF;UAAAC,QAAA,EAClG;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACX,EAAA,CAjFID,MAAgB;EAAA,QACML,OAAO;AAAA;AAAA0B,EAAA,GAD7BrB,MAAgB;AAmFtB,eAAeA,MAAM;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}