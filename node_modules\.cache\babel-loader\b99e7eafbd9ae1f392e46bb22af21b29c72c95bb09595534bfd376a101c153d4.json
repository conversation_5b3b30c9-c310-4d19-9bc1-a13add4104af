{"ast": null, "code": "import { createClient } from '@supabase/supabase-js';\nconst supabaseUrl = process.env.REACT_APP_SUPABASE_URL || '';\nconst supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY || '';\nif (!supabaseUrl || !supabaseAnonKey) {\n  console.error('Missing Supabase environment variables');\n}\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey);", "map": {"version": 3, "names": ["createClient", "supabaseUrl", "process", "env", "REACT_APP_SUPABASE_URL", "supabaseAnonKey", "REACT_APP_SUPABASE_ANON_KEY", "console", "error", "supabase"], "sources": ["D:/GrowthSch/IGCSEStuGuide/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\n\nconst supabaseUrl = process.env.REACT_APP_SUPABASE_URL || '';\nconst supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY || '';\n\nif (!supabaseUrl || !supabaseAnonKey) {\n  console.error('Missing Supabase environment variables');\n}\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey);"], "mappings": "AAAA,SAASA,YAAY,QAAQ,uBAAuB;AAEpD,MAAMC,WAAW,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,EAAE;AAC5D,MAAMC,eAAe,GAAGH,OAAO,CAACC,GAAG,CAACG,2BAA2B,IAAI,EAAE;AAErE,IAAI,CAACL,WAAW,IAAI,CAACI,eAAe,EAAE;EACpCE,OAAO,CAACC,KAAK,CAAC,wCAAwC,CAAC;AACzD;AAEA,OAAO,MAAMC,QAAQ,GAAGT,YAAY,CAACC,WAAW,EAAEI,eAAe,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}