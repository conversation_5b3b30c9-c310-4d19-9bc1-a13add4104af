{"ast": null, "code": "'use strict';\n\n// https://tc39.es/ecma262/#sec-IsHTMLDDA-internal-slot\nvar documentAll = typeof document == 'object' && document.all;\n\n// `IsCallable` abstract operation\n// https://tc39.es/ecma262/#sec-iscallable\n// eslint-disable-next-line unicorn/no-typeof-undefined -- required for testing\nmodule.exports = typeof documentAll == 'undefined' && documentAll !== undefined ? function (argument) {\n  return typeof argument == 'function' || argument === documentAll;\n} : function (argument) {\n  return typeof argument == 'function';\n};", "map": {"version": 3, "names": ["documentAll", "document", "all", "module", "exports", "undefined", "argument"], "sources": ["D:/GrowthSch/IGCSEStuGuide/node_modules/core-js-pure/internals/is-callable.js"], "sourcesContent": ["'use strict';\n// https://tc39.es/ecma262/#sec-IsHTMLDDA-internal-slot\nvar documentAll = typeof document == 'object' && document.all;\n\n// `IsCallable` abstract operation\n// https://tc39.es/ecma262/#sec-iscallable\n// eslint-disable-next-line unicorn/no-typeof-undefined -- required for testing\nmodule.exports = typeof documentAll == 'undefined' && documentAll !== undefined ? function (argument) {\n  return typeof argument == 'function' || argument === documentAll;\n} : function (argument) {\n  return typeof argument == 'function';\n};\n"], "mappings": "AAAA,YAAY;;AACZ;AACA,IAAIA,WAAW,GAAG,OAAOC,QAAQ,IAAI,QAAQ,IAAIA,QAAQ,CAACC,GAAG;;AAE7D;AACA;AACA;AACAC,MAAM,CAACC,OAAO,GAAG,OAAOJ,WAAW,IAAI,WAAW,IAAIA,WAAW,KAAKK,SAAS,GAAG,UAAUC,QAAQ,EAAE;EACpG,OAAO,OAAOA,QAAQ,IAAI,UAAU,IAAIA,QAAQ,KAAKN,WAAW;AAClE,CAAC,GAAG,UAAUM,QAAQ,EAAE;EACtB,OAAO,OAAOA,QAAQ,IAAI,UAAU;AACtC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}