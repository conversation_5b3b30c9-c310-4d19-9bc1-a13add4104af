import React from 'react';
import { useAuth } from '../contexts/AuthContext';

const DashboardPage: React.FC = () => {
  const { user } = useAuth();
  
  return (
    <div>
      <div className="mb-8">
        <h1>Your Dashboard</h1>
        <p className="text-neutral-600">Welcome back, {user?.email?.split('@')[0] || 'Student'}!</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-lg font-semibold mb-2">Study Streak</h3>
          <p className="text-3xl font-bold text-primary-600">3 days</p>
          <p className="text-neutral-500 text-sm mt-1">Keep it going!</p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-lg font-semibold mb-2">Flashcards Reviewed</h3>
          <p className="text-3xl font-bold text-primary-600">24</p>
          <p className="text-neutral-500 text-sm mt-1">This week</p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-lg font-semibold mb-2">Quizzes Completed</h3>
          <p className="text-3xl font-bold text-primary-600">5</p>
          <p className="text-neutral-500 text-sm mt-1">This week</p>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-lg font-semibold mb-2">Average Score</h3>
          <p className="text-3xl font-bold text-primary-600">78%</p>
          <p className="text-neutral-500 text-sm mt-1">Across all quizzes</p>
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-xl font-semibold mb-4">Recent Activity</h2>
            <div className="space-y-4">
              <div className="border-l-4 border-primary-500 pl-4 py-1">
                <p className="font-medium">Completed Biology Quiz: Cell Structure</p>
                <p className="text-sm text-neutral-500">Score: 85% • 2 hours ago</p>
              </div>
              <div className="border-l-4 border-secondary-500 pl-4 py-1">
                <p className="font-medium">Reviewed Chemistry Flashcards</p>
                <p className="text-sm text-neutral-500">15 cards • Yesterday</p>
              </div>
              <div className="border-l-4 border-primary-500 pl-4 py-1">
                <p className="font-medium">Completed Physics Quiz: Forces</p>
                <p className="text-sm text-neutral-500">Score: 72% • 2 days ago</p>
              </div>
            </div>
          </div>
        </div>
        
        <div>
          <div className="bg-white p-6 rounded