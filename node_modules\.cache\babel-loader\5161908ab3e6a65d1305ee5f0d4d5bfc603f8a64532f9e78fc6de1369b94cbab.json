{"ast": null, "code": "import { AuthClient } from '@supabase/auth-js';\nexport class SupabaseAuthClient extends AuthClient {\n  constructor(options) {\n    super(options);\n  }\n}", "map": {"version": 3, "names": ["AuthClient", "SupabaseAuthClient", "constructor", "options"], "sources": ["D:\\GrowthSch\\IGCSEStuGuide\\node_modules\\@supabase\\supabase-js\\src\\lib\\SupabaseAuthClient.ts"], "sourcesContent": ["import { AuthClient } from '@supabase/auth-js'\nimport { SupabaseAuthClientOptions } from './types'\n\nexport class SupabaseAuthClient extends AuthClient {\n  constructor(options: SupabaseAuthClientOptions) {\n    super(options)\n  }\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,mBAAmB;AAG9C,OAAM,MAAOC,kBAAmB,SAAQD,UAAU;EAChDE,YAAYC,OAAkC;IAC5C,KAAK,CAACA,OAAO,CAAC;EAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}